{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 27554, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 27554, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 27554, "tid": 6700, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 27554, "tid": 6700, "ts": 1748533449546060, "dur": 672, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 27554, "tid": 6700, "ts": 1748533449552296, "dur": 1122, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 27554, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 27554, "tid": 1, "ts": 1748533448493339, "dur": 11714, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 27554, "tid": 1, "ts": 1748533448505058, "dur": 108751, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 27554, "tid": 1, "ts": 1748533448613823, "dur": 47964, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 27554, "tid": 6700, "ts": 1748533449553422, "dur": 36, "ph": "X", "name": "", "args": {}}, {"pid": 27554, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448490202, "dur": 4720, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448494925, "dur": 1040200, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448496119, "dur": 3740, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448499868, "dur": 1593, "ph": "X", "name": "ProcessMessages 56", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448501465, "dur": 12531, "ph": "X", "name": "ReadAsync 56", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448514005, "dur": 641, "ph": "X", "name": "ProcessMessages 5772", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448514650, "dur": 73, "ph": "X", "name": "ReadAsync 5772", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448514724, "dur": 3, "ph": "X", "name": "ProcessMessages 8176", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448514729, "dur": 286, "ph": "X", "name": "ReadAsync 8176", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448515017, "dur": 2, "ph": "X", "name": "ProcessMessages 2636", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448515020, "dur": 61, "ph": "X", "name": "ReadAsync 2636", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448515084, "dur": 121, "ph": "X", "name": "ReadAsync 543", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448515615, "dur": 1, "ph": "X", "name": "ProcessMessages 1436", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448515617, "dur": 33, "ph": "X", "name": "ReadAsync 1436", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448515652, "dur": 3, "ph": "X", "name": "ProcessMessages 8161", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448515656, "dur": 176, "ph": "X", "name": "ReadAsync 8161", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448515836, "dur": 23, "ph": "X", "name": "ProcessMessages 3097", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448515911, "dur": 49, "ph": "X", "name": "ReadAsync 3097", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448515960, "dur": 1, "ph": "X", "name": "ProcessMessages 1434", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448515962, "dur": 100, "ph": "X", "name": "ReadAsync 1434", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448516065, "dur": 3, "ph": "X", "name": "ProcessMessages 1406", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448516070, "dur": 81, "ph": "X", "name": "ReadAsync 1406", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448516154, "dur": 4, "ph": "X", "name": "ProcessMessages 2121", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448516160, "dur": 384, "ph": "X", "name": "ReadAsync 2121", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448516545, "dur": 1, "ph": "X", "name": "ProcessMessages 2232", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448516548, "dur": 75, "ph": "X", "name": "ReadAsync 2232", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448516624, "dur": 2, "ph": "X", "name": "ProcessMessages 3416", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448516627, "dur": 764, "ph": "X", "name": "ReadAsync 3416", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448517393, "dur": 2, "ph": "X", "name": "ProcessMessages 4009", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448517396, "dur": 68, "ph": "X", "name": "ReadAsync 4009", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448517465, "dur": 19, "ph": "X", "name": "ProcessMessages 409", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448517485, "dur": 169, "ph": "X", "name": "ReadAsync 409", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448517656, "dur": 1, "ph": "X", "name": "ProcessMessages 1840", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448517658, "dur": 444, "ph": "X", "name": "ReadAsync 1840", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448518104, "dur": 1, "ph": "X", "name": "ProcessMessages 1025", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448518117, "dur": 29, "ph": "X", "name": "ReadAsync 1025", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448518149, "dur": 653, "ph": "X", "name": "ReadAsync 1675", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448518808, "dur": 12, "ph": "X", "name": "ProcessMessages 6367", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448518822, "dur": 449, "ph": "X", "name": "ReadAsync 6367", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448519272, "dur": 4, "ph": "X", "name": "ProcessMessages 6722", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448519277, "dur": 86, "ph": "X", "name": "ReadAsync 6722", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448519369, "dur": 3, "ph": "X", "name": "ProcessMessages 1010", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448519374, "dur": 142, "ph": "X", "name": "ReadAsync 1010", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448519519, "dur": 2, "ph": "X", "name": "ProcessMessages 1109", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448519523, "dur": 56, "ph": "X", "name": "ReadAsync 1109", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448519581, "dur": 3, "ph": "X", "name": "ProcessMessages 1427", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448519586, "dur": 166, "ph": "X", "name": "ReadAsync 1427", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448519756, "dur": 4, "ph": "X", "name": "ProcessMessages 2493", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448519763, "dur": 320, "ph": "X", "name": "ReadAsync 2493", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448520085, "dur": 3, "ph": "X", "name": "ProcessMessages 6220", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448520089, "dur": 309, "ph": "X", "name": "ReadAsync 6220", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448520401, "dur": 6, "ph": "X", "name": "ProcessMessages 3635", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448520409, "dur": 54, "ph": "X", "name": "ReadAsync 3635", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448520466, "dur": 1, "ph": "X", "name": "ProcessMessages 485", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448520467, "dur": 154, "ph": "X", "name": "ReadAsync 485", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448520624, "dur": 4, "ph": "X", "name": "ProcessMessages 2080", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448520629, "dur": 90, "ph": "X", "name": "ReadAsync 2080", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448520722, "dur": 3, "ph": "X", "name": "ProcessMessages 2340", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448520727, "dur": 74, "ph": "X", "name": "ReadAsync 2340", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448520812, "dur": 3, "ph": "X", "name": "ProcessMessages 2320", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448520816, "dur": 69, "ph": "X", "name": "ReadAsync 2320", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448520889, "dur": 20, "ph": "X", "name": "ProcessMessages 2419", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448520911, "dur": 92, "ph": "X", "name": "ReadAsync 2419", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448521006, "dur": 2, "ph": "X", "name": "ProcessMessages 1416", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448521009, "dur": 101, "ph": "X", "name": "ReadAsync 1416", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448521113, "dur": 2, "ph": "X", "name": "ProcessMessages 1572", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448521117, "dur": 76, "ph": "X", "name": "ReadAsync 1572", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448521195, "dur": 2, "ph": "X", "name": "ProcessMessages 1800", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448521199, "dur": 61, "ph": "X", "name": "ReadAsync 1800", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448521263, "dur": 3, "ph": "X", "name": "ProcessMessages 1626", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448521267, "dur": 69, "ph": "X", "name": "ReadAsync 1626", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448521338, "dur": 2, "ph": "X", "name": "ProcessMessages 1585", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448521342, "dur": 79, "ph": "X", "name": "ReadAsync 1585", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448521423, "dur": 4, "ph": "X", "name": "ProcessMessages 1923", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448521429, "dur": 60, "ph": "X", "name": "ReadAsync 1923", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448521491, "dur": 2, "ph": "X", "name": "ProcessMessages 1533", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448521495, "dur": 99, "ph": "X", "name": "ReadAsync 1533", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448521597, "dur": 3, "ph": "X", "name": "ProcessMessages 1522", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448521601, "dur": 88, "ph": "X", "name": "ReadAsync 1522", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448521691, "dur": 1, "ph": "X", "name": "ProcessMessages 1271", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448521693, "dur": 42, "ph": "X", "name": "ReadAsync 1271", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448521737, "dur": 1, "ph": "X", "name": "ProcessMessages 1153", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448521739, "dur": 40, "ph": "X", "name": "ReadAsync 1153", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448521781, "dur": 72, "ph": "X", "name": "ReadAsync 755", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448521855, "dur": 1, "ph": "X", "name": "ProcessMessages 1224", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448521857, "dur": 47, "ph": "X", "name": "ReadAsync 1224", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448521905, "dur": 1, "ph": "X", "name": "ProcessMessages 1721", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448521920, "dur": 62, "ph": "X", "name": "ReadAsync 1721", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448521985, "dur": 3, "ph": "X", "name": "ProcessMessages 980", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448521989, "dur": 706, "ph": "X", "name": "ReadAsync 980", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448522696, "dur": 5, "ph": "X", "name": "ProcessMessages 8104", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448522727, "dur": 102, "ph": "X", "name": "ReadAsync 8104", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448522831, "dur": 3, "ph": "X", "name": "ProcessMessages 1984", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448522836, "dur": 465, "ph": "X", "name": "ReadAsync 1984", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448523303, "dur": 1, "ph": "X", "name": "ProcessMessages 612", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448523305, "dur": 70, "ph": "X", "name": "ReadAsync 612", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448523376, "dur": 2, "ph": "X", "name": "ProcessMessages 5338", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448523380, "dur": 149, "ph": "X", "name": "ReadAsync 5338", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448523530, "dur": 1, "ph": "X", "name": "ProcessMessages 2596", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448523532, "dur": 382, "ph": "X", "name": "ReadAsync 2596", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448523917, "dur": 7, "ph": "X", "name": "ProcessMessages 4286", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448524417, "dur": 45, "ph": "X", "name": "ReadAsync 4286", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448524463, "dur": 3, "ph": "X", "name": "ProcessMessages 8145", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448524467, "dur": 362, "ph": "X", "name": "ReadAsync 8145", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448524833, "dur": 9, "ph": "X", "name": "ProcessMessages 5787", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448524843, "dur": 500, "ph": "X", "name": "ReadAsync 5787", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448525379, "dur": 7, "ph": "X", "name": "ProcessMessages 3551", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448525420, "dur": 37, "ph": "X", "name": "ReadAsync 3551", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448525458, "dur": 2, "ph": "X", "name": "ProcessMessages 4706", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448525461, "dur": 27, "ph": "X", "name": "ReadAsync 4706", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448525491, "dur": 1385, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448526920, "dur": 37, "ph": "X", "name": "ProcessMessages 559", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448526991, "dur": 226, "ph": "X", "name": "ReadAsync 559", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448527246, "dur": 73, "ph": "X", "name": "ProcessMessages 6023", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448527321, "dur": 670, "ph": "X", "name": "ReadAsync 6023", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448527996, "dur": 3, "ph": "X", "name": "ProcessMessages 705", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448528002, "dur": 673, "ph": "X", "name": "ReadAsync 705", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448528762, "dur": 3, "ph": "X", "name": "ProcessMessages 511", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448528766, "dur": 2700, "ph": "X", "name": "ReadAsync 511", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448531469, "dur": 3, "ph": "X", "name": "ProcessMessages 6201", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448531473, "dur": 259, "ph": "X", "name": "ReadAsync 6201", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448531735, "dur": 249, "ph": "X", "name": "ReadAsync 837", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448531987, "dur": 248, "ph": "X", "name": "ReadAsync 975", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448532238, "dur": 395, "ph": "X", "name": "ReadAsync 1364", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448532636, "dur": 784, "ph": "X", "name": "ReadAsync 918", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448533422, "dur": 1, "ph": "X", "name": "ProcessMessages 816", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448533433, "dur": 1022, "ph": "X", "name": "ReadAsync 816", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448534460, "dur": 1, "ph": "X", "name": "ProcessMessages 804", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448534474, "dur": 686, "ph": "X", "name": "ReadAsync 804", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448536410, "dur": 3, "ph": "X", "name": "ProcessMessages 1476", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448536414, "dur": 35843, "ph": "X", "name": "ReadAsync 1476", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448572278, "dur": 4, "ph": "X", "name": "ProcessMessages 1996", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448572287, "dur": 147, "ph": "X", "name": "ReadAsync 1996", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448572436, "dur": 4, "ph": "X", "name": "ProcessMessages 8125", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448572441, "dur": 92, "ph": "X", "name": "ReadAsync 8125", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448572537, "dur": 158, "ph": "X", "name": "ReadAsync 763", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448572698, "dur": 1484, "ph": "X", "name": "ReadAsync 601", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448574184, "dur": 61, "ph": "X", "name": "ProcessMessages 816", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448574246, "dur": 36403, "ph": "X", "name": "ReadAsync 816", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448610662, "dur": 8, "ph": "X", "name": "ProcessMessages 8169", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448610671, "dur": 72, "ph": "X", "name": "ReadAsync 8169", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448610746, "dur": 210, "ph": "X", "name": "ReadAsync 512", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448610959, "dur": 776, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448611739, "dur": 710, "ph": "X", "name": "ReadAsync 891", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448612454, "dur": 134, "ph": "X", "name": "ReadAsync 517", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448612591, "dur": 228, "ph": "X", "name": "ReadAsync 317", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448612821, "dur": 238, "ph": "X", "name": "ReadAsync 550", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448613061, "dur": 706, "ph": "X", "name": "ReadAsync 371", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448613770, "dur": 252, "ph": "X", "name": "ReadAsync 957", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448614025, "dur": 303, "ph": "X", "name": "ReadAsync 936", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448614335, "dur": 55, "ph": "X", "name": "ReadAsync 595", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448614392, "dur": 303, "ph": "X", "name": "ReadAsync 377", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448614698, "dur": 119, "ph": "X", "name": "ReadAsync 550", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448614820, "dur": 478, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448615300, "dur": 17, "ph": "X", "name": "ReadAsync 511", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448615320, "dur": 198, "ph": "X", "name": "ReadAsync 338", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448615520, "dur": 184, "ph": "X", "name": "ReadAsync 520", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448615706, "dur": 499, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448616208, "dur": 582, "ph": "X", "name": "ReadAsync 858", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448616793, "dur": 570, "ph": "X", "name": "ReadAsync 912", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448617366, "dur": 60, "ph": "X", "name": "ReadAsync 945", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448617433, "dur": 260, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448617695, "dur": 163, "ph": "X", "name": "ReadAsync 577", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448617869, "dur": 369, "ph": "X", "name": "ReadAsync 341", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448618240, "dur": 148, "ph": "X", "name": "ReadAsync 469", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448618389, "dur": 1, "ph": "X", "name": "ProcessMessages 1679", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448618391, "dur": 18, "ph": "X", "name": "ReadAsync 1679", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448618412, "dur": 522, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448618936, "dur": 35, "ph": "X", "name": "ReadAsync 499", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448618979, "dur": 716, "ph": "X", "name": "ReadAsync 347", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448619697, "dur": 140, "ph": "X", "name": "ReadAsync 586", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448619840, "dur": 676, "ph": "X", "name": "ReadAsync 365", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448620519, "dur": 628, "ph": "X", "name": "ReadAsync 849", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448621150, "dur": 652, "ph": "X", "name": "ReadAsync 861", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448621804, "dur": 2, "ph": "X", "name": "ProcessMessages 2771", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448621807, "dur": 29, "ph": "X", "name": "ReadAsync 2771", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448621888, "dur": 19, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448621909, "dur": 1, "ph": "X", "name": "ProcessMessages 1632", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448621911, "dur": 579, "ph": "X", "name": "ReadAsync 1632", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448622492, "dur": 283, "ph": "X", "name": "ReadAsync 508", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448622778, "dur": 757, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448623537, "dur": 47, "ph": "X", "name": "ReadAsync 1037", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448623585, "dur": 1, "ph": "X", "name": "ProcessMessages 1242", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448623587, "dur": 416, "ph": "X", "name": "ReadAsync 1242", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448624006, "dur": 1933, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448625941, "dur": 4, "ph": "X", "name": "ProcessMessages 7361", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448625946, "dur": 29, "ph": "X", "name": "ReadAsync 7361", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448625976, "dur": 295, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448626272, "dur": 66, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448626344, "dur": 6, "ph": "X", "name": "ProcessMessages 384", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448626351, "dur": 36, "ph": "X", "name": "ReadAsync 384", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448626412, "dur": 54, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448626468, "dur": 72, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448626551, "dur": 37, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448626593, "dur": 93, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448626688, "dur": 30, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448626720, "dur": 58, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448626780, "dur": 106, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448626888, "dur": 40, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448626930, "dur": 63, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448626997, "dur": 39, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448627049, "dur": 26, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448627076, "dur": 74, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448627152, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448627155, "dur": 55, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448627212, "dur": 40, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448627254, "dur": 107, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448627364, "dur": 28, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448627400, "dur": 95, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448627497, "dur": 185, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448627684, "dur": 38, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448627916, "dur": 1, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448627918, "dur": 26, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448627945, "dur": 1, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448627946, "dur": 72, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448628020, "dur": 85, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448628107, "dur": 226, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448628334, "dur": 1, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448628335, "dur": 99, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448628436, "dur": 22, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448628461, "dur": 26, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448628489, "dur": 48, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448628539, "dur": 94, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448628641, "dur": 68, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448628711, "dur": 141, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448628855, "dur": 55, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448628944, "dur": 363, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448629310, "dur": 20, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448629332, "dur": 31, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448629366, "dur": 286, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448629654, "dur": 89, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448629745, "dur": 98, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448629845, "dur": 50, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448629903, "dur": 51, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448629955, "dur": 105, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448630063, "dur": 23, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448630087, "dur": 261, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448630350, "dur": 1, "ph": "X", "name": "ProcessMessages 320", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448630351, "dur": 26, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448630380, "dur": 26, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448630408, "dur": 72, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448630484, "dur": 81, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448630567, "dur": 100, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448630670, "dur": 147, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448630818, "dur": 104, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448630925, "dur": 401, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448631327, "dur": 1, "ph": "X", "name": "ProcessMessages 400", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448631329, "dur": 24, "ph": "X", "name": "ReadAsync 400", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448631355, "dur": 1, "ph": "X", "name": "ProcessMessages 336", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448631357, "dur": 78, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448631436, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448631438, "dur": 85, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448631524, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448631527, "dur": 45, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448631574, "dur": 40, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448631616, "dur": 16, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448631634, "dur": 61, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448631753, "dur": 51, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448631807, "dur": 73, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448631882, "dur": 87, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448631971, "dur": 324, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448632297, "dur": 2, "ph": "X", "name": "ProcessMessages 592", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448632300, "dur": 89, "ph": "X", "name": "ReadAsync 592", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448632391, "dur": 81, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448632474, "dur": 28, "ph": "X", "name": "ReadAsync 172", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448632504, "dur": 65, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448632572, "dur": 184, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448632759, "dur": 53, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448632814, "dur": 24, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448632840, "dur": 95, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448632938, "dur": 24, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448632967, "dur": 28, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448633001, "dur": 51, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448633055, "dur": 90, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448633147, "dur": 95, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448633244, "dur": 214, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448633470, "dur": 43, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448633515, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448633516, "dur": 106, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448633624, "dur": 79, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448633705, "dur": 2574, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448636282, "dur": 117, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448636401, "dur": 5, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448636407, "dur": 1225, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448637634, "dur": 99, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448637735, "dur": 348, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448638094, "dur": 1900, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448639997, "dur": 16603, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448656613, "dur": 55, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448656670, "dur": 55, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448656727, "dur": 3043, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448659775, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448659776, "dur": 9808, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448669589, "dur": 59, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448669650, "dur": 295, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448669948, "dur": 119, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448670074, "dur": 3, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448670079, "dur": 547, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448670631, "dur": 180, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448670819, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448670822, "dur": 140, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448671057, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448671061, "dur": 167, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448671231, "dur": 1, "ph": "X", "name": "ProcessMessages 28", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448671242, "dur": 64, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448671309, "dur": 77, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448671388, "dur": 34, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448671425, "dur": 149, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448671576, "dur": 165, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448671744, "dur": 88, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448671834, "dur": 517, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448672370, "dur": 236, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448672609, "dur": 75, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448672686, "dur": 134, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448672824, "dur": 323, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448673164, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448673167, "dur": 217, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448673387, "dur": 182, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448673571, "dur": 321, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448673923, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448673927, "dur": 103, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448674033, "dur": 134, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448674170, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448674173, "dur": 134, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448674309, "dur": 164, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448674476, "dur": 363, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448674842, "dur": 39, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448674894, "dur": 28, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448674965, "dur": 900, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448675869, "dur": 2256, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448678513, "dur": 7, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448678523, "dur": 333, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448678915, "dur": 8, "ph": "X", "name": "ProcessMessages 208", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448678924, "dur": 238, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448679172, "dur": 1, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448679175, "dur": 498, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448679682, "dur": 503, "ph": "X", "name": "ProcessMessages 24", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448680425, "dur": 89, "ph": "X", "name": "ReadAsync 24", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448680519, "dur": 1, "ph": "X", "name": "ProcessMessages 304", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448680521, "dur": 115, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448680639, "dur": 27, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448680669, "dur": 149, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448680862, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448680875, "dur": 242, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448681120, "dur": 255, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448681376, "dur": 66, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448681447, "dur": 116, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448681565, "dur": 277, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448681845, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448681848, "dur": 736, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448682587, "dur": 42, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448682661, "dur": 25, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448682690, "dur": 228, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448682920, "dur": 255, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448683233, "dur": 153, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448683392, "dur": 57, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448683450, "dur": 47, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448683499, "dur": 122, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448683624, "dur": 150, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448683780, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448683784, "dur": 543, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448684349, "dur": 203, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448684555, "dur": 678, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448685238, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448685243, "dur": 128, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448685373, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448685375, "dur": 396, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448685781, "dur": 495, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448686279, "dur": 392, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448686673, "dur": 1614, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448688290, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448688293, "dur": 265, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448688564, "dur": 3, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448688569, "dur": 297, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448688869, "dur": 426, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448689299, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448689302, "dur": 547, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448689876, "dur": 219, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448690098, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448690101, "dur": 520, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448690624, "dur": 3, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448690629, "dur": 109294, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448799929, "dur": 1, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448799932, "dur": 52, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448799986, "dur": 44, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448800118, "dur": 37, "ph": "X", "name": "ReadAsync 7477", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448800157, "dur": 24, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448800190, "dur": 20, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448800213, "dur": 34, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448800249, "dur": 3890, "ph": "X", "name": "ProcessMessages 2800", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448804142, "dur": 255, "ph": "X", "name": "ReadAsync 2800", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448804401, "dur": 1661, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448806065, "dur": 479, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448806550, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448806554, "dur": 462, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448807019, "dur": 5, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448807025, "dur": 286, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448807314, "dur": 365, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448807684, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448807687, "dur": 691, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448808380, "dur": 1262, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448809653, "dur": 285, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448809945, "dur": 2, "ph": "X", "name": "ProcessMessages 28", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448809949, "dur": 68, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448810020, "dur": 935, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448810957, "dur": 394, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448811354, "dur": 653, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448812013, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448812030, "dur": 195, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448812228, "dur": 1029, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448813259, "dur": 1385, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448814647, "dur": 301, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448814950, "dur": 474, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448815426, "dur": 193, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448815622, "dur": 981, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448816606, "dur": 290, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448816898, "dur": 1205, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448818106, "dur": 1025, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448819143, "dur": 598, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448819749, "dur": 449, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448820201, "dur": 911, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448821113, "dur": 268, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448821419, "dur": 87, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448821513, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448821517, "dur": 478, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448821997, "dur": 332, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448822332, "dur": 904, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448823239, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448823242, "dur": 842, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448824115, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448824118, "dur": 194, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448824315, "dur": 189, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448824506, "dur": 1149, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448825658, "dur": 921, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448826588, "dur": 85, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448826675, "dur": 173, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448826851, "dur": 49, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448826901, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448826903, "dur": 27, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448826932, "dur": 87, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448827022, "dur": 45, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448827070, "dur": 22, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448827094, "dur": 29, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448827125, "dur": 22, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448827149, "dur": 57, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448827209, "dur": 47, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448827258, "dur": 31, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448827290, "dur": 5, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448827296, "dur": 26, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448827325, "dur": 33, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448827360, "dur": 43, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448827405, "dur": 21, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448827428, "dur": 89, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448827520, "dur": 20, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448827546, "dur": 29, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448827578, "dur": 53, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448827633, "dur": 25, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448827660, "dur": 4, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448827665, "dur": 15, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448827682, "dur": 19, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448827703, "dur": 47, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448827753, "dur": 128, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448827883, "dur": 84, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448827970, "dur": 385, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448828357, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448828358, "dur": 21, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448828381, "dur": 78, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448828462, "dur": 24, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448828488, "dur": 28, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448828519, "dur": 60, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448828581, "dur": 85, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448828667, "dur": 68, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448828738, "dur": 81, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448828821, "dur": 27, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448828856, "dur": 22, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448828880, "dur": 75, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448828956, "dur": 8, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448828965, "dur": 58, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448829026, "dur": 55, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448829083, "dur": 65, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448829150, "dur": 67, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448829219, "dur": 40, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448829261, "dur": 50, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448829314, "dur": 29, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448829345, "dur": 73, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448829421, "dur": 34, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448829457, "dur": 591, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448830049, "dur": 1, "ph": "X", "name": "ProcessMessages 204", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448830050, "dur": 37, "ph": "X", "name": "ReadAsync 204", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448830090, "dur": 172, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448830264, "dur": 234, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533448830507, "dur": 417958, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533449248475, "dur": 24, "ph": "X", "name": "ProcessMessages 1854", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533449248501, "dur": 1641, "ph": "X", "name": "ReadAsync 1854", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533449250145, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533449250146, "dur": 1402, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533449251552, "dur": 3, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533449251555, "dur": 64, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533449251622, "dur": 270275, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533449521905, "dur": 4, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533449521910, "dur": 49, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533449521962, "dur": 65, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533449522031, "dur": 39, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533449522072, "dur": 258, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533449522332, "dur": 37, "ph": "X", "name": "ProcessMessages 8005", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533449522370, "dur": 43, "ph": "X", "name": "ReadAsync 8005", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533449522430, "dur": 36, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533449522468, "dur": 31, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533449522500, "dur": 5, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533449522506, "dur": 23, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533449522531, "dur": 76, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533449522614, "dur": 13, "ph": "X", "name": "ProcessMessages 5299", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533449522628, "dur": 6789, "ph": "X", "name": "ReadAsync 5299", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533449529422, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533449529425, "dur": 139, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533449529565, "dur": 1, "ph": "X", "name": "ProcessMessages 40", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533449529568, "dur": 1655, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533449531224, "dur": 36, "ph": "X", "name": "ProcessMessages 92", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533449531262, "dur": 42, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533449531306, "dur": 182, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533449531490, "dur": 1039, "ph": "X", "name": "ProcessMessages 25", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748533449532532, "dur": 2521, "ph": "X", "name": "ReadAsync 25", "args": {}}, {"pid": 27554, "tid": 6700, "ts": 1748533449553460, "dur": 1352, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 27554, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 27554, "tid": 8589934592, "ts": 1748533448485225, "dur": 176608, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 27554, "tid": 8589934592, "ts": 1748533448661837, "dur": 6, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 27554, "tid": 8589934592, "ts": 1748533448661845, "dur": 2042, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 27554, "tid": 6700, "ts": 1748533449554814, "dur": 17, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 27554, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 27554, "tid": 4294967296, "ts": 1748533448400324, "dur": 1137418, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 27554, "tid": 4294967296, "ts": 1748533448408544, "dur": 71693, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 27554, "tid": 4294967296, "ts": 1748533449537894, "dur": 4927, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 27554, "tid": 4294967296, "ts": 1748533449541138, "dur": 46, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 27554, "tid": 4294967296, "ts": 1748533449542944, "dur": 19, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 27554, "tid": 6700, "ts": 1748533449554833, "dur": 18, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1748533448489242, "dur": 3618, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748533448492875, "dur": 20672, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748533448513601, "dur": 157, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1748533448513758, "dur": 107, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748533448514511, "dur": 484, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_FD466DE9C9D83358.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748533448515079, "dur": 88, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_5D1BAF9C8C0FFD1F.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748533448515225, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_D09E55B94B6FBE69.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748533448515754, "dur": 135, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_7B6D90F67FD0A1D6.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748533448522494, "dur": 417, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1748533448524280, "dur": 422, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Shaders.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1748533448524789, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Rendering.LightTransport.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1748533448525518, "dur": 58, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualStudio.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1748533448525645, "dur": 62, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748533448525915, "dur": 86, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEngine.TestRunner.dll"}}, {"pid": 12345, "tid": 0, "ts": 1748533448526354, "dur": 69, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748533448526880, "dur": 63, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 0, "ts": 1748533448527853, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1748533448528576, "dur": 81, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEditor.UI.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1748533448528658, "dur": 67, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.AI.Navigation.Editor.ConversionSystem.dll"}}, {"pid": 12345, "tid": 0, "ts": 1748533448539911, "dur": 32743, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.TestFramework.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1748533448572673, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.TestFramework.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1748533448577451, "dur": 33438, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Core.Runtime.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1748533448513871, "dur": 111852, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748533448625731, "dur": 905739, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748533449531577, "dur": 58, "ph": "X", "name": "BuildQueueDestroyTail", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748533449531667, "dur": 819, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1748533448513829, "dur": 111910, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748533448625742, "dur": 183, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 1, "ts": 1748533448625933, "dur": 307, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EmbreeModule.dll_33EF23AB874F1C0F.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748533448626244, "dur": 114, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EmbreeModule.dll_33EF23AB874F1C0F.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748533448626378, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteMaskModule.dll_1011BD4CAF44A078.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748533448626454, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748533448626509, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_0F150CB94F2F22E2.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748533448626594, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748533448626645, "dur": 104, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UmbraModule.dll_6AD82FAF92A13AC6.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748533448626749, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748533448626833, "dur": 183, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_73BBD5453873209A.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748533448627017, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748533448627085, "dur": 139, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_18DC2EF7A0439C61.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748533448627224, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748533448627336, "dur": 98, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HierarchyCoreModule.dll_A894F34BC96CE2D6.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748533448627434, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748533448627504, "dur": 123, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_13682AC6E2C4EE7B.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748533448627627, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748533448627694, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_AB798D326E6716CC.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748533448627792, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748533448627855, "dur": 135, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_6ECCF42616E9E2B7.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748533448628051, "dur": 89, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UmbraModule.dll_3104EC13C41E6B6B.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748533448628140, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748533448628254, "dur": 129, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_DAACB4F719294F92.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748533448628383, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748533448628462, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_42EA6A51FFD2E09A.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748533448628547, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748533448628695, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Antlr3.Runtime.dll_9790B5B5B9BAF4F9.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748533448628819, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748533448628929, "dur": 109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AMDModule.dll_7ADE8004EB70550A.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748533448629038, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748533448629184, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748533448629247, "dur": 277, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748533448629546, "dur": 295, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748533448629867, "dur": 224, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.CodeGen.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 1, "ts": 1748533448630092, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748533448630221, "dur": 90, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Pdb.dll_336F3065DC28AE59.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748533448630341, "dur": 197, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.CodeGen.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748533448630565, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748533448630647, "dur": 140, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748533448630787, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748533448630913, "dur": 170, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748533448631083, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748533448631308, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748533448631471, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748533448631530, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748533448631662, "dur": 149, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748533448631813, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748533448631922, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748533448632046, "dur": 172, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748533448632398, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748533448632454, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748533448632570, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748533448632674, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748533448632794, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748533448632850, "dur": 124, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748533448632975, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748533448633061, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748533448633156, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748533448633266, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748533448633364, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748533448633544, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748533448633635, "dur": 1297, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748533448634932, "dur": 750, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748533448635682, "dur": 834, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748533448636516, "dur": 758, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748533448637274, "dur": 795, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748533448638069, "dur": 756, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748533448638825, "dur": 791, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748533448639616, "dur": 751, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748533448640367, "dur": 724, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748533448641091, "dur": 762, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748533448641853, "dur": 729, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748533448642582, "dur": 886, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748533448643469, "dur": 757, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748533448644226, "dur": 897, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748533448645123, "dur": 914, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748533448646037, "dur": 720, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748533448646758, "dur": 749, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748533448647507, "dur": 693, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748533448648200, "dur": 770, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748533448648970, "dur": 766, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748533448649736, "dur": 710, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748533448650447, "dur": 798, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748533448651245, "dur": 743, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748533448651988, "dur": 722, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748533448652710, "dur": 733, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748533448653443, "dur": 727, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748533448654170, "dur": 702, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748533448654872, "dur": 715, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748533448655587, "dur": 749, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748533448656336, "dur": 902, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748533448657238, "dur": 734, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748533448657972, "dur": 939, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748533448658911, "dur": 1500, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748533448660412, "dur": 1025, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748533448661437, "dur": 1023, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748533448662461, "dur": 1441, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748533448664177, "dur": 544, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "/Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/Tools/BuildPipeline"}}, {"pid": 12345, "tid": 1, "ts": 1748533448664721, "dur": 2126, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "/Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/Tools/Compilation/Unity.ILPP.Runner"}}, {"pid": 12345, "tid": 1, "ts": 1748533448666848, "dur": 689, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "/Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/Tools/Compilation/Unity.ILPP.Trigger"}}, {"pid": 12345, "tid": 1, "ts": 1748533448663902, "dur": 3635, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748533448667537, "dur": 1068, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748533448668605, "dur": 842, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748533448669447, "dur": 59, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748533448669650, "dur": 1032, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.AI.Navigation.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748533448670720, "dur": 1024, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.AI.Navigation.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748533448671745, "dur": 204, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748533448671985, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748533448672085, "dur": 197, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748533448672283, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748533448672340, "dur": 762, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748533448673102, "dur": 228, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748533448673337, "dur": 119, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748533448673484, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748533448673602, "dur": 127, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/FMODUnityResonance.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748533448673779, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748533448673907, "dur": 217, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748533448674149, "dur": 98, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748533448674273, "dur": 117, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Cursor.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748533448674396, "dur": 139, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748533448674540, "dur": 1158, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748533448675699, "dur": 602, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748533448676371, "dur": 797, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Rendering.LightTransport.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748533448677169, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748533448677273, "dur": 2359, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748533448679633, "dur": 400, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748533448680036, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/FMODUnityResonanceEditor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748533448680146, "dur": 487, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.PerformanceTesting.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748533448680633, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748533448680692, "dur": 926, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.PerformanceTesting.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748533448681619, "dur": 171, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748533448681852, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.PerformanceTesting.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748533448681932, "dur": 474, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.PerformanceTesting.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748533448682406, "dur": 160, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748533448682570, "dur": 89, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/FMODUnity.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748533448682692, "dur": 878, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/FMODUnity.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748533448683571, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748533448683649, "dur": 826, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/FMODUnityResonance.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748533448684475, "dur": 170, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748533448684651, "dur": 778, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/FMODUnityResonanceEditor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748533448685465, "dur": 2116, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748533448687583, "dur": 171, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/FMODUnityEditor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748533448687778, "dur": 824, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/FMODUnityEditor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748533448688661, "dur": 113137, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748533448801799, "dur": 2738, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.AI.Navigation.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748533448804539, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748533448804630, "dur": 2532, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Core.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748533448807162, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748533448807260, "dur": 2357, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.GPUDriven.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748533448809619, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748533448809683, "dur": 2516, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748533448812200, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748533448812282, "dur": 2650, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748533448814945, "dur": 3668, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.InputSystem.DocCodeSamples.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748533448818658, "dur": 3668, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748533448822361, "dur": 2932, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.PlasticSCM.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748533448825306, "dur": 5115, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/FMODUnityEditor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748533448830442, "dur": 701040, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748533448513834, "dur": 111910, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748533448625745, "dur": 159, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1748533448625927, "dur": 290, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_DB357A1C5B5CB80B.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748533448626318, "dur": 236, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_FED4EADA5A496243.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748533448626554, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748533448626636, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_7EFD851680B8C482.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748533448626709, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748533448626834, "dur": 114, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AccessibilityModule.dll_A5396449E1C706BB.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748533448626948, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748533448627015, "dur": 117, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_1D189CF611149EDC.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748533448627187, "dur": 154, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_2793B80490192449.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748533448627341, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748533448627422, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputForUIModule.dll_2A46121DF039C105.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748533448627507, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748533448627567, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_23B09F05B98149F7.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748533448627686, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_3EDBDDC0989E0EE4.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748533448627770, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748533448627841, "dur": 119, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_7B6D90F67FD0A1D6.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748533448627960, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748533448628033, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_7FABF25E82FCDDCD.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748533448628110, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748533448628212, "dur": 146, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_2AF15A5FC73B3288.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748533448628358, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748533448628445, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_0240B089C917E98F.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748533448628565, "dur": 89, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.OSXStandalone.Extensions.dll_E38DB30ED3E699A5.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748533448628722, "dur": 128, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.IonicZip.dll_C359BB03E2AB95AC.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748533448628850, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748533448628951, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748533448629039, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748533448629173, "dur": 312, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 2, "ts": 1748533448629531, "dur": 350, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1748533448629953, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748533448630036, "dur": 317, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1748533448630562, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748533448630629, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748533448630744, "dur": 147, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1748533448630891, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748533448630976, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748533448631109, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748533448631263, "dur": 145, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Flow.Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1748533448631476, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748533448631584, "dur": 159, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748533448631747, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748533448631870, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748533448631964, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748533448632053, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748533448632123, "dur": 193, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.PlasticSCM.Editor.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1748533448632393, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748533448632449, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748533448632565, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748533448632647, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748533448632783, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748533448632846, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748533448632954, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748533448633030, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748533448633144, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748533448633221, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748533448633315, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748533448633400, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748533448633508, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748533448633569, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748533448633686, "dur": 872, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748533448634558, "dur": 795, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748533448635353, "dur": 810, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748533448636163, "dur": 818, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748533448636981, "dur": 699, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748533448637680, "dur": 866, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748533448638546, "dur": 743, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748533448639290, "dur": 735, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748533448640025, "dur": 769, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748533448640795, "dur": 724, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748533448641519, "dur": 771, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748533448642291, "dur": 882, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748533448643175, "dur": 698, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748533448643874, "dur": 804, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748533448644679, "dur": 1029, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748533448645708, "dur": 710, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748533448646419, "dur": 730, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748533448647150, "dur": 708, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748533448647858, "dur": 696, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748533448648555, "dur": 797, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748533448649352, "dur": 744, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748533448650096, "dur": 711, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748533448650807, "dur": 796, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748533448651603, "dur": 738, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748533448652341, "dur": 719, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748533448653061, "dur": 769, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748533448653830, "dur": 685, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748533448654515, "dur": 707, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748533448655223, "dur": 724, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748533448655947, "dur": 774, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748533448656768, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748533448656843, "dur": 796, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748533448657639, "dur": 789, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748533448658429, "dur": 1196, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748533448659625, "dur": 1372, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748533448660998, "dur": 1149, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748533448662147, "dur": 1329, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748533448663476, "dur": 1393, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748533448664869, "dur": 1288, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748533448666158, "dur": 1266, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748533448667424, "dur": 1088, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748533448668512, "dur": 787, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748533448669402, "dur": 131, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748533448669534, "dur": 169, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748533448669705, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748533448669802, "dur": 4670, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748533448674472, "dur": 558, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748533448675081, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748533448675182, "dur": 145, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748533448675346, "dur": 2091, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748533448677437, "dur": 270, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748533448677756, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748533448677840, "dur": 1708, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748533448679548, "dur": 460, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748533448680014, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.RenderPipelines.GPUDriven.Runtime.ref.dll_059BC5464FF14C2B.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748533448680117, "dur": 391, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748533448680548, "dur": 2113, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748533448682661, "dur": 426, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748533448683116, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.RenderPipelines.Core.Editor.ref.dll_57B051C6BAF6DF9D.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748533448683217, "dur": 205, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748533448683450, "dur": 2195, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748533448685645, "dur": 197, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748533448685869, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748533448685946, "dur": 587, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748533448686533, "dur": 136, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748533448686683, "dur": 88, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/PPv2URPConverters.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748533448686796, "dur": 547, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/PPv2URPConverters.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748533448687344, "dur": 188, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748533448687619, "dur": 114168, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748533448801808, "dur": 2176, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.InputSystem.TestFramework.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748533448804031, "dur": 3014, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Mathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748533448807046, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748533448807155, "dur": 2943, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.PerformanceTesting.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748533448810124, "dur": 3359, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/FMODUnity.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748533448813498, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748533448813568, "dur": 3432, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.AI.Navigation.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748533448817053, "dur": 3107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/FMODUnityResonanceEditor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748533448820193, "dur": 2961, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Cursor.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748533448823155, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748533448823231, "dur": 2699, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/PPv2URPConverters.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748533448825982, "dur": 4671, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.CollabProxy.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748533448830669, "dur": 700768, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748533448513835, "dur": 111919, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748533448625756, "dur": 461, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.BuildProfileModule.dll_5A44DDE874DD43A2.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748533448626226, "dur": 173, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GIModule.dll_CD898CC368A4D4E9.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748533448626431, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SubstanceModule.dll_00177D20A4B9F3ED.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748533448626505, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748533448626559, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIAutomationModule.dll_0AE5CCD911510C62.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748533448626677, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VFXModule.dll_EE2B3985795E2BC5.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748533448626758, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748533448626842, "dur": 115, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_FD466DE9C9D83358.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748533448626995, "dur": 102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_7FC5D5B6D1C0F474.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748533448627097, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748533448627160, "dur": 155, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.dll_CF90820960D75094.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748533448627315, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748533448627389, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_A6062A75CA920C08.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748533448627483, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748533448627542, "dur": 110, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MarshallingModule.dll_282E744BB5E921C6.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748533448627652, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748533448627741, "dur": 91, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ShaderVariantAnalyticsModule.dll_50D38B6F9A6D24D8.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748533448627832, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748533448627887, "dur": 115, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_242B47D8D2C44484.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748533448628039, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_6F2F737294BA2646.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748533448628170, "dur": 176, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_1C9B2F49E4A7FD04.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748533448628346, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748533448628456, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_C20199F83674ABBE.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748533448628520, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748533448628607, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Collections.LowLevel.ILSupport.dll_A8DBA71237A1D37F.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748533448628683, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748533448628761, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748533448628827, "dur": 191, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.UI.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748533448629018, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748533448629102, "dur": 268, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 3, "ts": 1748533448629378, "dur": 464, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748533448629931, "dur": 218, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.rsp2"}}, {"pid": 12345, "tid": 3, "ts": 1748533448630150, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748533448630239, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.dll_E81F8781B3EEDB59.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748533448630336, "dur": 186, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.CodeGen.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748533448630575, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748533448630700, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748533448630779, "dur": 195, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Rendering.LightTransport.Runtime.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748533448630974, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748533448631093, "dur": 135, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Runtime.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748533448631309, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748533448631482, "dur": 260, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748533448631743, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748533448631849, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748533448631939, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748533448632033, "dur": 156, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Common.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748533448632410, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748533448632471, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748533448632583, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748533448632702, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748533448632861, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748533448632961, "dur": 135, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748533448633101, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748533448633163, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748533448633285, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748533448633378, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748533448633561, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748533448633673, "dur": 900, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748533448634573, "dur": 789, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748533448635362, "dur": 815, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748533448636177, "dur": 819, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748533448636996, "dur": 702, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748533448637698, "dur": 865, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748533448638563, "dur": 747, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748533448639310, "dur": 720, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748533448640030, "dur": 746, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748533448640777, "dur": 739, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748533448641517, "dur": 776, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748533448642293, "dur": 884, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748533448643177, "dur": 683, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748533448643861, "dur": 782, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748533448644644, "dur": 922, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748533448645566, "dur": 818, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748533448646384, "dur": 754, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748533448647138, "dur": 723, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748533448647861, "dur": 698, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748533448648559, "dur": 788, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748533448649347, "dur": 740, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748533448650087, "dur": 706, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748533448650793, "dur": 804, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748533448651597, "dur": 735, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748533448652332, "dur": 723, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748533448653055, "dur": 759, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748533448653815, "dur": 695, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748533448654510, "dur": 706, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748533448655216, "dur": 709, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748533448655925, "dur": 789, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748533448656748, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748533448656817, "dur": 786, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748533448657603, "dur": 775, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748533448658378, "dur": 1109, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748533448659487, "dur": 1424, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748533448660911, "dur": 900, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748533448661811, "dur": 1255, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748533448663066, "dur": 1515, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748533448664581, "dur": 1221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748533448665802, "dur": 1117, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748533448666919, "dur": 1261, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748533448668181, "dur": 851, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748533448669032, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748533448669257, "dur": 131, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748533448669388, "dur": 82, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748533448669493, "dur": 746, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748533448670239, "dur": 446, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748533448670685, "dur": 287, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748533448670977, "dur": 90, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.AI.Navigation.Editor.ConversionSystem.ref.dll_B533E0F362656246.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748533448671067, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748533448671133, "dur": 301, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748533448671467, "dur": 2074, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748533448673542, "dur": 457, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748533448674013, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748533448674078, "dur": 314, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Rendering.LightTransport.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748533448674398, "dur": 202, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Collections.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748533448674616, "dur": 161, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748533448674784, "dur": 790, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748533448675574, "dur": 471, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748533448676053, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748533448676108, "dur": 1406, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748533448677515, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748533448677648, "dur": 1227, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748533448678875, "dur": 237, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748533448679128, "dur": 1081, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748533448680210, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748533448680471, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748533448680531, "dur": 145, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualStudio.Editor.ref.dll_F37C0F1BA2EC04B9.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748533448680676, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748533448680801, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748533448681026, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.TestFramework.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748533448681133, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748533448681234, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.DocCodeSamples.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748533448681317, "dur": 788, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.TestFramework.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748533448682106, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748533448682184, "dur": 1488, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748533448683673, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748533448683786, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748533448683987, "dur": 3598, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748533448687585, "dur": 2227, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748533448689814, "dur": 333, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748533448690166, "dur": 111625, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748533448801796, "dur": 4883, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748533448806707, "dur": 3088, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Rendering.LightTransport.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748533448809796, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748533448809853, "dur": 3520, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748533448813387, "dur": 3439, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748533448816872, "dur": 2436, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Rendering.LightTransport.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748533448819308, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748533448819392, "dur": 2140, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748533448821533, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748533448821602, "dur": 2633, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748533448824235, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748533448824346, "dur": 4089, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Multiplayer.Center.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748533448828435, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748533448828554, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Timeline.Editor.pdb"}}, {"pid": 12345, "tid": 3, "ts": 1748533448828917, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748533448829025, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748533448829138, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748533448829300, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748533448829397, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748533448829593, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748533448829770, "dur": 232, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748533448830064, "dur": 419264, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748533449249522, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 3, "ts": 1748533449249330, "dur": 1494, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748533449251549, "dur": 230, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748533449521941, "dur": 823, "ph": "X", "name": "EmitNodeFinish", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748533449252106, "dur": 270663, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/200b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748533449529643, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 3, "ts": 1748533449529707, "dur": 1694, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 3, "ts": 1748533449531402, "dur": 64, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748533448513837, "dur": 111921, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748533448625759, "dur": 468, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreBusinessMetricsModule.dll_BA0B4FF91CE5F8A1.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748533448626234, "dur": 171, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphicsStateCollectionSerializerModule.dll_C9A2ED22AC71DAA3.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748533448626449, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TerrainModule.dll_7C523B7BB1DAFB72.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748533448626548, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TreeModule.dll_0726B0E54E5C8C45.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748533448626657, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_557EEB5E99C128BC.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748533448626737, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748533448626829, "dur": 129, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AdaptivePerformanceModule.dll_3A20567EF69EC9A7.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748533448626996, "dur": 103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_B6303FFB36F7BFF8.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748533448627100, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748533448627167, "dur": 164, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_F44CFBD4B87405AF.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748533448627331, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748533448627412, "dur": 108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_EE8FDF61FD2953D9.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748533448627520, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748533448627612, "dur": 139, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_B2F5068140D067F0.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748533448627811, "dur": 89, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_D95259C03EE6A7C4.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748533448627900, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748533448628052, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748533448628123, "dur": 108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_42DE6F6D94492967.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748533448628232, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748533448628353, "dur": 112, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_5836CD6851EC9E19.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748533448628523, "dur": 279, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748533448628802, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748533448628864, "dur": 443, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748533448629314, "dur": 8268, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748533448637583, "dur": 203, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748533448637892, "dur": 2198, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748533448640128, "dur": 16272, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748533448656402, "dur": 232, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748533448656700, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748533448656775, "dur": 2934, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748533448659711, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748533448659840, "dur": 9468, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748533448669309, "dur": 142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748533448669519, "dur": 118, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748533448669687, "dur": 1713, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748533448671400, "dur": 466, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748533448671873, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.ref.dll_A43E80D48102A6F4.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748533448671989, "dur": 295, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Collections.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748533448672324, "dur": 1625, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Collections.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748533448673950, "dur": 293, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748533448674286, "dur": 115, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Collections.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748533448674438, "dur": 429, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Collections.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748533448674868, "dur": 193, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748533448675067, "dur": 3155, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 4, "ts": 1748533448678223, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748533448678383, "dur": 800, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748533448799913, "dur": 500, "ph": "X", "name": "EmitNodeFinish", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748533448679584, "dur": 120863, "ph": "X", "name": "ILPP-Configuration", "args": {"detail": "Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 4, "ts": 1748533448801766, "dur": 2446, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Universal.2D.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748533448804212, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748533448804323, "dur": 3383, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748533448807746, "dur": 3933, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748533448811681, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748533448811748, "dur": 3381, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Collections.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748533448815129, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748533448815248, "dur": 3460, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Timeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748533448818709, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748533448818816, "dur": 2511, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.SettingsProvider.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748533448821367, "dur": 3298, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.AI.Navigation.Editor.ConversionSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748533448824705, "dur": 5524, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748533448830269, "dur": 699201, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748533449529546, "dur": 1945, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748533448513846, "dur": 111915, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748533448625764, "dur": 471, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_A97ABBD6C696F706.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748533448626237, "dur": 115, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_A97ABBD6C696F706.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748533448626365, "dur": 339, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SketchUpModule.dll_DBF9BD3B0D79F91E.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748533448626754, "dur": 112, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VideoModule.dll_150D0801C80CAAF8.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748533448626866, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748533448626955, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_2D2E9A4895907F63.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748533448627014, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748533448627096, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_CC9B8F296E9C6231.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748533448627175, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748533448627250, "dur": 149, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GraphicsStateCollectionSerializerModule.dll_D296BA1279712408.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748533448627448, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_1CA607ABAD2FE49C.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748533448627545, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748533448627622, "dur": 130, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_71635C60905BE81B.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748533448627803, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_9927031187632C7E.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748533448627868, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748533448627988, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_8D74880C622CE1AD.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748533448628044, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748533448628113, "dur": 138, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_43500FCF65A89BF3.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748533448628251, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748533448628360, "dur": 99, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_66BE93EAC03D0429.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748533448628505, "dur": 253, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.UI.rsp2"}}, {"pid": 12345, "tid": 5, "ts": 1748533448628759, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748533448628821, "dur": 206, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.UI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1748533448629028, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748533448629124, "dur": 193, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748533448629328, "dur": 6747, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748533448636076, "dur": 312, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748533448636395, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UI.ref.dll_79746E5030301F94.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748533448636448, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748533448636533, "dur": 764, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748533448637297, "dur": 658, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748533448638043, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748533448638101, "dur": 752, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748533448638853, "dur": 754, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748533448639607, "dur": 755, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748533448640362, "dur": 709, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748533448641071, "dur": 758, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748533448641830, "dur": 727, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748533448642557, "dur": 882, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748533448643441, "dur": 710, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748533448644152, "dur": 876, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748533448645028, "dur": 952, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748533448645980, "dur": 685, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748533448646665, "dur": 763, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748533448647428, "dur": 679, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748533448648107, "dur": 730, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748533448648837, "dur": 792, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748533448649629, "dur": 712, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748533448650341, "dur": 732, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748533448651073, "dur": 804, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748533448651877, "dur": 697, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748533448652574, "dur": 753, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748533448653327, "dur": 725, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748533448654052, "dur": 691, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748533448654743, "dur": 718, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748533448655461, "dur": 754, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748533448656216, "dur": 910, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748533448657126, "dur": 761, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748533448657887, "dur": 896, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748533448658784, "dur": 1485, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748533448660270, "dur": 1071, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748533448661341, "dur": 969, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748533448662310, "dur": 1322, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748533448663632, "dur": 1429, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748533448665062, "dur": 1318, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748533448666381, "dur": 1233, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748533448667615, "dur": 1050, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748533448668665, "dur": 578, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748533448669244, "dur": 62, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748533448669488, "dur": 97, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748533448669585, "dur": 422, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Utilities.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748533448670039, "dur": 555, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Utilities.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748533448670595, "dur": 317, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748533448670915, "dur": 116, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.ShaderGraph.Utilities.ref.dll_9587680413563429.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748533448671032, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748533448671165, "dur": 392, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748533448671583, "dur": 2597, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748533448674180, "dur": 1037, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748533448675220, "dur": 995, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748533448676252, "dur": 6608, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748533448682861, "dur": 198, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748533448683137, "dur": 378, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748533448683554, "dur": 1734, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748533448685288, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748533448685393, "dur": 100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748533448685499, "dur": 286, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748533448685813, "dur": 1803, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748533448687616, "dur": 114163, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748533448801780, "dur": 3073, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748533448804853, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748533448804915, "dur": 3196, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.State.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748533448808159, "dur": 2964, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.InputSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748533448811157, "dur": 2958, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748533448814116, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748533448814211, "dur": 2468, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.ShaderGraph.Utilities.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748533448816679, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748533448816750, "dur": 2830, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.ShaderGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748533448819582, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748533448819639, "dur": 4176, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Collections.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748533448823816, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748533448823902, "dur": 5834, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.PerformanceTesting.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748533448829831, "dur": 467, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748533448830299, "dur": 699346, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748533449529706, "dur": 1719, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 6, "ts": 1748533448513853, "dur": 111979, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748533448625836, "dur": 407, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_576EED2B1C54EF9B.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748533448626246, "dur": 91, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_576EED2B1C54EF9B.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748533448626338, "dur": 121, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.ShaderFoundryModule.dll_3DB4BB1D7CF8DED1.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748533448626460, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748533448626527, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TilemapModule.dll_1B4EA2FCC997DC7B.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748533448626640, "dur": 191, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_3FD7D90864D1DB0B.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748533448626831, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748533448626938, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_24BD6C6FEF5EF086.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748533448627046, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_5D1BAF9C8C0FFD1F.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748533448627149, "dur": 143, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DirectorModule.dll_EEFB9A6B4613348E.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748533448627292, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748533448627401, "dur": 99, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_C90DAD4D0E34B513.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748533448627554, "dur": 101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MultiplayerModule.dll_FF6DF2438A8881EC.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748533448627655, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748533448627757, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_4D1EEC228562B063.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748533448627837, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748533448627919, "dur": 103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_458C519AD2A9288F.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748533448628067, "dur": 88, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_4E33C3C55AB9064E.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748533448628155, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748533448628301, "dur": 108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_90196DBEEEA0E95D.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748533448628410, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748533448628526, "dur": 255, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1748533448628781, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748533448628855, "dur": 175, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.NVIDIAModule.dll_8C003B92E4EC36BD.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748533448629083, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748533448629268, "dur": 104, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1748533448629383, "dur": 102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 6, "ts": 1748533448629506, "dur": 227, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.rsp2"}}, {"pid": 12345, "tid": 6, "ts": 1748533448629778, "dur": 227, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.CodeGen.rsp2"}}, {"pid": 12345, "tid": 6, "ts": 1748533448630076, "dur": 139, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.CompilationPipeline.Common.dll_18DD15F206BB0EF4.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748533448630253, "dur": 131, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1748533448630437, "dur": 189, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.rsp2"}}, {"pid": 12345, "tid": 6, "ts": 1748533448630627, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748533448630737, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748533448630840, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748533448630930, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748533448631035, "dur": 135, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748533448631320, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748533448631417, "dur": 159, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.Editor.rsp2"}}, {"pid": 12345, "tid": 6, "ts": 1748533448631576, "dur": 141, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748533448631719, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748533448631846, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748533448631925, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748533448632028, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748533448632109, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748533448632416, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748533448632544, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748533448632631, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748533448632761, "dur": 108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.ForUI.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1748533448632870, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748533448633004, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748533448633133, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748533448633213, "dur": 142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748533448633358, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748533448633443, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748533448633529, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748533448633618, "dur": 927, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748533448634545, "dur": 797, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748533448635342, "dur": 816, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748533448636158, "dur": 816, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748533448636975, "dur": 701, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748533448637676, "dur": 842, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748533448638518, "dur": 733, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748533448639251, "dur": 725, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748533448639976, "dur": 757, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748533448640733, "dur": 725, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748533448641458, "dur": 761, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748533448642219, "dur": 882, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748533448643101, "dur": 706, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748533448643809, "dur": 821, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748533448644630, "dur": 916, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748533448645546, "dur": 816, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748533448646362, "dur": 748, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748533448647110, "dur": 707, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748533448647817, "dur": 690, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748533448648507, "dur": 798, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748533448649306, "dur": 744, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748533448650050, "dur": 703, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748533448650753, "dur": 781, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748533448651534, "dur": 737, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748533448652272, "dur": 717, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748533448652989, "dur": 728, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748533448653717, "dur": 712, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748533448654429, "dur": 709, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748533448655138, "dur": 712, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748533448655850, "dur": 745, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748533448656596, "dur": 882, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748533448657478, "dur": 784, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748533448658264, "dur": 999, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748533448659263, "dur": 1487, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748533448660750, "dur": 970, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748533448661720, "dur": 1135, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748533448662856, "dur": 1600, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748533448664456, "dur": 1281, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748533448665738, "dur": 1124, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748533448666862, "dur": 1349, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748533448668211, "dur": 832, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748533448669043, "dur": 316, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748533448669386, "dur": 244, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748533448669632, "dur": 282, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748533448669915, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748533448670016, "dur": 95, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748533448670162, "dur": 514, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748533448670677, "dur": 242, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748533448670922, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Timeline.ref.dll_D292A1F7EB1601C3.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748533448671025, "dur": 278, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748533448671345, "dur": 1128, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748533448672474, "dur": 267, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748533448672744, "dur": 358, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748533448673103, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748533448673164, "dur": 1336, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748533448674500, "dur": 522, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748533448675025, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Editor.ref.dll_EA1A6D1CB62C8BFD.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748533448675098, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748533448675179, "dur": 3019, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748533448678199, "dur": 211, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748533448678415, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748533448678522, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748533448678641, "dur": 1102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748533448679792, "dur": 834, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748533448680679, "dur": 1960, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748533448682640, "dur": 321, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748533448682968, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.TextMeshPro.Editor.ref.dll_C5B7873C632B963A.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748533448683104, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748533448683232, "dur": 115, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748533448683376, "dur": 1053, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748533448684429, "dur": 234, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748533448684738, "dur": 2814, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748533448687555, "dur": 178, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748533448687752, "dur": 714, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748533448688466, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748533448688704, "dur": 113107, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748533448801811, "dur": 4350, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Multiplayer.Center.Common.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748533448806163, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748533448806279, "dur": 3267, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Shaders.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748533448809547, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748533448809605, "dur": 2307, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/FMODUnityResonance.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748533448811912, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748533448811968, "dur": 3611, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Config.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748533448815579, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748533448815715, "dur": 2526, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Searcher.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748533448818242, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748533448818299, "dur": 3617, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.State.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748533448821916, "dur": 145, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748533448822065, "dur": 2363, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.Flow.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748533448824429, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748533448824515, "dur": 5425, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.Shared.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748533448830028, "dur": 640, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748533448830676, "dur": 700782, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748533448513860, "dur": 111976, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748533448625839, "dur": 380, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_B0CD21B816834329.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748533448626223, "dur": 99, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_B0CD21B816834329.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748533448626323, "dur": 91, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_94E7D34F0A24E8EE.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748533448626462, "dur": 242, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_C72CD647BFAB69C4.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748533448626704, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748533448626760, "dur": 102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.XRModule.dll_B651CB6D5431C551.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748533448626863, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748533448626947, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_F4C05CFCBFAEF91C.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748533448627034, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748533448627106, "dur": 104, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_D09E55B94B6FBE69.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748533448627211, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748533448627299, "dur": 113, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_AA0CAB1786F0F828.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748533448627413, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748533448627468, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_9191DFAD59FAEA6A.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748533448627546, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748533448627652, "dur": 108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_54CEF74303048885.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748533448627760, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748533448627827, "dur": 105, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_B5FD6E098A39A5D2.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748533448627933, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748533448628022, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_796E436A12A0D05D.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748533448628155, "dur": 174, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_FB9A8EFABECD3061.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748533448628329, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748533448628413, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_DB39C465F989DB9D.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748533448628481, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748533448628556, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_90AD9150DA4AFB38.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748533448628653, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748533448628731, "dur": 128, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.YamlDotNet.dll_4228628CD1B97490.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748533448628859, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748533448628984, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748533448629054, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748533448629185, "dur": 289, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.rsp2"}}, {"pid": 12345, "tid": 7, "ts": 1748533448629495, "dur": 236, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.rsp2"}}, {"pid": 12345, "tid": 7, "ts": 1748533448629765, "dur": 202, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748533448630097, "dur": 136, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Rocks.dll_C7D65B20A28C6399.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748533448630233, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748533448630338, "dur": 271, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748533448630609, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748533448630730, "dur": 190, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748533448630920, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748533448630985, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748533448631129, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748533448631515, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748533448631656, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748533448631791, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748533448631909, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748533448632031, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748533448632109, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748533448632402, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748533448632464, "dur": 145, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.PlasticSCM.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748533448632609, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748533448632742, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748533448632841, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748533448632921, "dur": 112, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Rendering.LightTransport.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748533448633033, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748533448633129, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748533448633200, "dur": 128, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/FMODUnityResonanceEditor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748533448633328, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748533448633423, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748533448633521, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748533448633641, "dur": 197, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748533448633840, "dur": 740, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748533448634581, "dur": 788, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748533448635369, "dur": 803, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748533448636172, "dur": 822, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748533448636994, "dur": 695, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748533448637689, "dur": 884, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748533448638573, "dur": 734, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748533448639307, "dur": 733, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748533448640040, "dur": 760, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748533448640800, "dur": 723, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748533448641523, "dur": 780, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748533448642303, "dur": 883, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748533448643186, "dur": 710, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748533448643898, "dur": 810, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748533448644708, "dur": 1011, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748533448645719, "dur": 717, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748533448646436, "dur": 748, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748533448647184, "dur": 702, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748533448647886, "dur": 698, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748533448648584, "dur": 790, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748533448649374, "dur": 754, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748533448650128, "dur": 708, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748533448650836, "dur": 785, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748533448651621, "dur": 725, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748533448652346, "dur": 726, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748533448653072, "dur": 762, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748533448653834, "dur": 689, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748533448654523, "dur": 709, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748533448655232, "dur": 721, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748533448655953, "dur": 775, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748533448656728, "dur": 854, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748533448657583, "dur": 800, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748533448658383, "dur": 1099, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748533448659483, "dur": 1414, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748533448660897, "dur": 902, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748533448661799, "dur": 1231, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748533448663030, "dur": 1549, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748533448664579, "dur": 1266, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748533448665845, "dur": 1202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748533448667047, "dur": 1214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748533448668261, "dur": 863, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748533448669124, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748533448669365, "dur": 155, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748533448669523, "dur": 294, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748533448669859, "dur": 955, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748533448670815, "dur": 664, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748533448671508, "dur": 91, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748533448671618, "dur": 896, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748533448672514, "dur": 291, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748533448672808, "dur": 362, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.AI.Navigation.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748533448673200, "dur": 1418, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.AI.Navigation.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748533448674618, "dur": 303, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748533448674944, "dur": 386, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.AI.Navigation.Updater.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748533448675331, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748533448675394, "dur": 760, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.AI.Navigation.Updater.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748533448676154, "dur": 191, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748533448676347, "dur": 392, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Collections.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748533448676739, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748533448676850, "dur": 1757, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Cursor.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748533448678608, "dur": 540, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748533448679152, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748533448679268, "dur": 947, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748533448680215, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748533448680362, "dur": 170, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748533448680533, "dur": 200, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748533448680736, "dur": 1346, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748533448682082, "dur": 286, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748533448682389, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748533448682455, "dur": 140, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748533448682622, "dur": 1738, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748533448684360, "dur": 421, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748533448684812, "dur": 2802, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748533448687614, "dur": 114153, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748533448801768, "dur": 2087, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Timeline.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748533448803914, "dur": 3881, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748533448807839, "dur": 3018, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.Flow.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748533448810859, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748533448810920, "dur": 3532, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.InputSystem.ForUI.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748533448814469, "dur": 3138, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748533448817651, "dur": 3441, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748533448821133, "dur": 2517, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.AI.Navigation.Updater.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748533448823687, "dur": 3068, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.TextMeshPro.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748533448828777, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748533448828896, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748533448829272, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748533448829440, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipeline.Universal.ShaderLibrary.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1748533448829507, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748533448829798, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748533448829850, "dur": 586, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748533448830450, "dur": 701039, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748533448513866, "dur": 111975, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748533448625841, "dur": 386, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_9CEC3B310263C3AF.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748533448626379, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteShapeModule.dll_9361338275291BF9.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748533448626456, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748533448626512, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextRenderingModule.dll_48780D15ACA6139C.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748533448626631, "dur": 90, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_AAC09D6387BA9514.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748533448626762, "dur": 95, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_317A56715ABF89E0.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748533448626880, "dur": 106, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.iOS.Extensions.Xcode.dll_173ADBFCCFB403B8.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748533448626986, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748533448627042, "dur": 95, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterInputModule.dll_D51B60744B48FB82.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748533448627137, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748533448627211, "dur": 149, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_0BF81E52D42208B4.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748533448627360, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748533448627431, "dur": 124, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_110AF0D569B2AA65.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748533448627555, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748533448627644, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_548678B7412B99C9.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748533448627773, "dur": 172, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_10C02A94B188B9C3.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748533448627945, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748533448628015, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_0DC95BA72EA6C3C0.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748533448628068, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748533448628132, "dur": 164, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_57DA0DA15F72E273.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748533448628296, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748533448628388, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_FA4CDBD8E90E39CA.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748533448628549, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WebGL.Extensions.dll_50F42E90481F425E.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748533448628749, "dur": 115, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.TextureAssets.dll_D9468BE09286D288.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748533448628864, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748533448628968, "dur": 207, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEditor.TestRunner.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748533448629175, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748533448629256, "dur": 460, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748533448629721, "dur": 161, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748533448629905, "dur": 210, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.CodeGen.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 8, "ts": 1748533448630115, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748533448630230, "dur": 89, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Mdb.dll_1C1CB7F65D73ACD6.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748533448630362, "dur": 290, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1748533448630652, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748533448630749, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748533448630873, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748533448630926, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748533448631020, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748533448631147, "dur": 148, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Editor.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748533448631332, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748533448631494, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748533448631616, "dur": 152, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748533448631771, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748533448631903, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748533448631976, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748533448632069, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748533448632151, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748533448632407, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748533448632475, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748533448632587, "dur": 135, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748533448632732, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748533448632812, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748533448632883, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748533448632943, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748533448633023, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748533448633179, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748533448633301, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748533448633381, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748533448633512, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748533448633579, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748533448633727, "dur": 836, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748533448634564, "dur": 796, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748533448635360, "dur": 808, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748533448636168, "dur": 819, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748533448636987, "dur": 728, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748533448637715, "dur": 853, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748533448638568, "dur": 734, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748533448639302, "dur": 769, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748533448640071, "dur": 743, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748533448640814, "dur": 717, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748533448641531, "dur": 794, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748533448642325, "dur": 868, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748533448643193, "dur": 714, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748533448643907, "dur": 808, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748533448644717, "dur": 1007, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748533448645724, "dur": 724, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748533448646448, "dur": 743, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748533448647191, "dur": 708, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748533448647899, "dur": 696, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748533448648595, "dur": 797, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748533448649392, "dur": 744, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748533448650136, "dur": 711, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748533448650847, "dur": 795, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748533448651642, "dur": 719, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748533448652361, "dur": 724, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748533448653085, "dur": 758, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748533448653843, "dur": 699, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748533448654542, "dur": 701, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748533448655244, "dur": 720, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748533448655964, "dur": 812, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748533448656776, "dur": 813, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748533448657589, "dur": 801, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748533448658390, "dur": 1128, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748533448659519, "dur": 1383, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748533448660903, "dur": 905, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748533448661808, "dur": 1238, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748533448663047, "dur": 1539, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748533448664586, "dur": 1243, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748533448665829, "dur": 1203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748533448667033, "dur": 1237, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748533448668270, "dur": 857, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748533448669127, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748533448669382, "dur": 187, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748533448669571, "dur": 1451, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Searcher.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748533448671099, "dur": 1438, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Searcher.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748533448672537, "dur": 402, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748533448673048, "dur": 283, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748533448673363, "dur": 1707, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748533448675071, "dur": 272, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748533448675379, "dur": 1219, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.ForUI.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748533448676654, "dur": 3041, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.ForUI.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748533448679696, "dur": 327, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748533448680028, "dur": 1513, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748533448681542, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748533448681646, "dur": 1182, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.DocCodeSamples.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748533448682828, "dur": 137, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748533448682994, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748533448683096, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748533448683154, "dur": 313, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748533448683485, "dur": 2608, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748533448686094, "dur": 216, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748533448686322, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748533448686412, "dur": 764, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748533448687176, "dur": 344, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748533448687543, "dur": 228, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748533448687780, "dur": 792, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748533448688573, "dur": 308, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748533448688886, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.State.Editor.ref.dll_BC6ADFF987BC82A3.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748533448688965, "dur": 174, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748533448689172, "dur": 409, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748533448689582, "dur": 174, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748533448689782, "dur": 259, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748533448690075, "dur": 447, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748533448691714, "dur": 556819, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748533449249321, "dur": 382, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748533449249719, "dur": 1108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748533449251412, "dur": 263, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748533449521897, "dur": 388, "ph": "X", "name": "EmitNodeFinish", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748533449252099, "dur": 270204, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/200b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748533449529469, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 8, "ts": 1748533449529549, "dur": 1911, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748533449533881, "dur": 907, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 27554, "tid": 6700, "ts": 1748533449555302, "dur": 5402, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 27554, "tid": 6700, "ts": 1748533449560742, "dur": 2808, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 27554, "tid": 6700, "ts": 1748533449549254, "dur": 86328, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}