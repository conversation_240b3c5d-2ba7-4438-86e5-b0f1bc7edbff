{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 27554, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 27554, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 27554, "tid": 6381, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 27554, "tid": 6381, "ts": 1748533079835407, "dur": 70, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 27554, "tid": 6381, "ts": 1748533079835505, "dur": 9, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 27554, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 27554, "tid": 1, "ts": 1748533078780397, "dur": 3513, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 27554, "tid": 1, "ts": 1748533078783918, "dur": 55309, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 27554, "tid": 1, "ts": 1748533078839230, "dur": 41642, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 27554, "tid": 6381, "ts": 1748533079835516, "dur": 53, "ph": "X", "name": "", "args": {}}, {"pid": 27554, "tid": 25769803776, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078780255, "dur": 19625, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078799883, "dur": 1033411, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078799927, "dur": 99, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078800030, "dur": 858, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078800924, "dur": 22, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078800946, "dur": 27121, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078828087, "dur": 5, "ph": "X", "name": "ProcessMessages 3995", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078828094, "dur": 85, "ph": "X", "name": "ReadAsync 3995", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078828203, "dur": 9, "ph": "X", "name": "ProcessMessages 3377", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078828215, "dur": 123, "ph": "X", "name": "ReadAsync 3377", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078828341, "dur": 5, "ph": "X", "name": "ProcessMessages 3251", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078828431, "dur": 108, "ph": "X", "name": "ReadAsync 3251", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078828563, "dur": 3, "ph": "X", "name": "ProcessMessages 5349", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078828567, "dur": 107, "ph": "X", "name": "ReadAsync 5349", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078828681, "dur": 7, "ph": "X", "name": "ProcessMessages 4139", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078828691, "dur": 114, "ph": "X", "name": "ReadAsync 4139", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078828870, "dur": 25, "ph": "X", "name": "ProcessMessages 4377", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078828900, "dur": 108, "ph": "X", "name": "ReadAsync 4377", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078829012, "dur": 3, "ph": "X", "name": "ProcessMessages 5654", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078829015, "dur": 940, "ph": "X", "name": "ReadAsync 5654", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078829997, "dur": 1, "ph": "X", "name": "ProcessMessages 1594", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078829999, "dur": 751, "ph": "X", "name": "ReadAsync 1594", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078830778, "dur": 43, "ph": "X", "name": "ProcessMessages 8182", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078830825, "dur": 113, "ph": "X", "name": "ReadAsync 8182", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078830945, "dur": 4, "ph": "X", "name": "ProcessMessages 3782", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078830952, "dur": 244, "ph": "X", "name": "ReadAsync 3782", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078831197, "dur": 1, "ph": "X", "name": "ProcessMessages 888", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078831202, "dur": 89, "ph": "X", "name": "ReadAsync 888", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078831293, "dur": 2, "ph": "X", "name": "ProcessMessages 3125", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078831296, "dur": 79, "ph": "X", "name": "ReadAsync 3125", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078831378, "dur": 170, "ph": "X", "name": "ReadAsync 1675", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078831551, "dur": 235, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078831802, "dur": 1, "ph": "X", "name": "ProcessMessages 1047", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078831804, "dur": 41, "ph": "X", "name": "ReadAsync 1047", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078831848, "dur": 2, "ph": "X", "name": "ProcessMessages 3963", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078831851, "dur": 93, "ph": "X", "name": "ReadAsync 3963", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078831945, "dur": 1, "ph": "X", "name": "ProcessMessages 1645", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078831947, "dur": 46, "ph": "X", "name": "ReadAsync 1645", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078831996, "dur": 194, "ph": "X", "name": "ReadAsync 243", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078832218, "dur": 11, "ph": "X", "name": "ProcessMessages 1745", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078832293, "dur": 43, "ph": "X", "name": "ReadAsync 1745", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078832337, "dur": 1, "ph": "X", "name": "ProcessMessages 2055", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078832340, "dur": 144, "ph": "X", "name": "ReadAsync 2055", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078832484, "dur": 1, "ph": "X", "name": "ProcessMessages 1127", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078832486, "dur": 41, "ph": "X", "name": "ReadAsync 1127", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078832553, "dur": 17, "ph": "X", "name": "ProcessMessages 1053", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078832580, "dur": 81, "ph": "X", "name": "ReadAsync 1053", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078832662, "dur": 1, "ph": "X", "name": "ProcessMessages 2040", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078832663, "dur": 143, "ph": "X", "name": "ReadAsync 2040", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078832807, "dur": 1, "ph": "X", "name": "ProcessMessages 957", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078832809, "dur": 46, "ph": "X", "name": "ReadAsync 957", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078832867, "dur": 1, "ph": "X", "name": "ProcessMessages 883", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078832869, "dur": 23, "ph": "X", "name": "ReadAsync 883", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078832897, "dur": 14, "ph": "X", "name": "ReadAsync 1013", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078832913, "dur": 20, "ph": "X", "name": "ReadAsync 531", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078832935, "dur": 90, "ph": "X", "name": "ReadAsync 536", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078833026, "dur": 1, "ph": "X", "name": "ProcessMessages 1900", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078833028, "dur": 235, "ph": "X", "name": "ReadAsync 1900", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078833265, "dur": 1, "ph": "X", "name": "ProcessMessages 2619", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078833267, "dur": 25, "ph": "X", "name": "ReadAsync 2619", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078833293, "dur": 1, "ph": "X", "name": "ProcessMessages 1003", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078833294, "dur": 66, "ph": "X", "name": "ReadAsync 1003", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078833363, "dur": 333, "ph": "X", "name": "ReadAsync 698", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078833734, "dur": 13, "ph": "X", "name": "ProcessMessages 1369", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078833749, "dur": 136, "ph": "X", "name": "ReadAsync 1369", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078833887, "dur": 1, "ph": "X", "name": "ProcessMessages 2671", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078833893, "dur": 71, "ph": "X", "name": "ReadAsync 2671", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078833976, "dur": 13, "ph": "X", "name": "ProcessMessages 2172", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078833991, "dur": 57, "ph": "X", "name": "ReadAsync 2172", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078834050, "dur": 1, "ph": "X", "name": "ProcessMessages 2602", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078834060, "dur": 32, "ph": "X", "name": "ReadAsync 2602", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078834094, "dur": 61, "ph": "X", "name": "ReadAsync 1226", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078834164, "dur": 1, "ph": "X", "name": "ProcessMessages 1840", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078834166, "dur": 25, "ph": "X", "name": "ReadAsync 1840", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078834193, "dur": 49, "ph": "X", "name": "ReadAsync 1026", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078834244, "dur": 27, "ph": "X", "name": "ReadAsync 1399", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078834274, "dur": 59, "ph": "X", "name": "ReadAsync 859", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078834336, "dur": 27, "ph": "X", "name": "ReadAsync 811", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078834372, "dur": 1, "ph": "X", "name": "ProcessMessages 1821", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078834374, "dur": 24, "ph": "X", "name": "ReadAsync 1821", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078834400, "dur": 81, "ph": "X", "name": "ReadAsync 900", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078834483, "dur": 1, "ph": "X", "name": "ProcessMessages 1931", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078834485, "dur": 32, "ph": "X", "name": "ReadAsync 1931", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078834526, "dur": 139, "ph": "X", "name": "ReadAsync 59", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078834682, "dur": 26, "ph": "X", "name": "ReadAsync 933", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078834710, "dur": 61, "ph": "X", "name": "ReadAsync 861", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078834774, "dur": 312, "ph": "X", "name": "ReadAsync 1213", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078835087, "dur": 1, "ph": "X", "name": "ProcessMessages 1127", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078835088, "dur": 80, "ph": "X", "name": "ReadAsync 1127", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078835170, "dur": 1, "ph": "X", "name": "ProcessMessages 1609", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078835171, "dur": 68, "ph": "X", "name": "ReadAsync 1609", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078835259, "dur": 123, "ph": "X", "name": "ReadAsync 611", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078835383, "dur": 1, "ph": "X", "name": "ProcessMessages 1364", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078835385, "dur": 26, "ph": "X", "name": "ReadAsync 1364", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078835417, "dur": 104, "ph": "X", "name": "ReadAsync 616", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078837424, "dur": 2, "ph": "X", "name": "ProcessMessages 1178", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078837428, "dur": 163, "ph": "X", "name": "ReadAsync 1178", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078837592, "dur": 2, "ph": "X", "name": "ProcessMessages 4008", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078837596, "dur": 33, "ph": "X", "name": "ReadAsync 4008", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078837631, "dur": 27, "ph": "X", "name": "ReadAsync 697", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078837661, "dur": 110, "ph": "X", "name": "ReadAsync 437", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078837772, "dur": 1, "ph": "X", "name": "ProcessMessages 1920", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078837773, "dur": 72, "ph": "X", "name": "ReadAsync 1920", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078837846, "dur": 1, "ph": "X", "name": "ProcessMessages 1958", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078837848, "dur": 36, "ph": "X", "name": "ReadAsync 1958", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078837886, "dur": 37, "ph": "X", "name": "ReadAsync 809", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078837925, "dur": 34, "ph": "X", "name": "ReadAsync 1251", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078837962, "dur": 61, "ph": "X", "name": "ReadAsync 674", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078838024, "dur": 1, "ph": "X", "name": "ProcessMessages 2088", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078838026, "dur": 45, "ph": "X", "name": "ReadAsync 2088", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078838073, "dur": 314, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078838388, "dur": 1, "ph": "X", "name": "ProcessMessages 2829", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078838391, "dur": 29, "ph": "X", "name": "ReadAsync 2829", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078838422, "dur": 48, "ph": "X", "name": "ReadAsync 1039", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078838472, "dur": 138, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078838612, "dur": 1, "ph": "X", "name": "ProcessMessages 990", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078838613, "dur": 41, "ph": "X", "name": "ReadAsync 990", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078838659, "dur": 1, "ph": "X", "name": "ProcessMessages 1110", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078838661, "dur": 70, "ph": "X", "name": "ReadAsync 1110", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078838732, "dur": 3, "ph": "X", "name": "ProcessMessages 1866", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078838735, "dur": 54, "ph": "X", "name": "ReadAsync 1866", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078838792, "dur": 41, "ph": "X", "name": "ReadAsync 1268", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078838836, "dur": 41, "ph": "X", "name": "ReadAsync 846", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078838880, "dur": 54, "ph": "X", "name": "ReadAsync 1199", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078838975, "dur": 55, "ph": "X", "name": "ReadAsync 581", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078839031, "dur": 1, "ph": "X", "name": "ProcessMessages 3647", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078839039, "dur": 23, "ph": "X", "name": "ReadAsync 3647", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078839065, "dur": 74, "ph": "X", "name": "ReadAsync 1133", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078839140, "dur": 1, "ph": "X", "name": "ProcessMessages 2496", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078839141, "dur": 32, "ph": "X", "name": "ReadAsync 2496", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078839175, "dur": 40, "ph": "X", "name": "ReadAsync 1093", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078839217, "dur": 22, "ph": "X", "name": "ReadAsync 1259", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078839241, "dur": 26, "ph": "X", "name": "ReadAsync 542", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078839269, "dur": 59, "ph": "X", "name": "ReadAsync 1047", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078839331, "dur": 19, "ph": "X", "name": "ReadAsync 1083", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078839352, "dur": 16, "ph": "X", "name": "ReadAsync 799", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078839370, "dur": 36, "ph": "X", "name": "ReadAsync 305", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078839409, "dur": 25, "ph": "X", "name": "ReadAsync 536", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078839436, "dur": 30, "ph": "X", "name": "ReadAsync 671", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078839467, "dur": 9, "ph": "X", "name": "ProcessMessages 920", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078839476, "dur": 22, "ph": "X", "name": "ReadAsync 920", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078839501, "dur": 55, "ph": "X", "name": "ReadAsync 732", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078839557, "dur": 1, "ph": "X", "name": "ProcessMessages 1816", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078839573, "dur": 66, "ph": "X", "name": "ReadAsync 1816", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078839654, "dur": 1, "ph": "X", "name": "ProcessMessages 1921", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078839690, "dur": 47, "ph": "X", "name": "ReadAsync 1921", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078839740, "dur": 141, "ph": "X", "name": "ReadAsync 1002", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078839882, "dur": 1, "ph": "X", "name": "ProcessMessages 1187", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078839884, "dur": 54, "ph": "X", "name": "ReadAsync 1187", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078839938, "dur": 1, "ph": "X", "name": "ProcessMessages 1224", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078839940, "dur": 53, "ph": "X", "name": "ReadAsync 1224", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078839994, "dur": 1, "ph": "X", "name": "ProcessMessages 1433", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078839995, "dur": 18, "ph": "X", "name": "ReadAsync 1433", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078840016, "dur": 33293, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078873370, "dur": 4, "ph": "X", "name": "ProcessMessages 3701", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078873384, "dur": 107, "ph": "X", "name": "ReadAsync 3701", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078873493, "dur": 198, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078873702, "dur": 1, "ph": "X", "name": "ProcessMessages 424", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078873711, "dur": 255, "ph": "X", "name": "ReadAsync 424", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078873973, "dur": 321, "ph": "X", "name": "ReadAsync 281", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078874303, "dur": 144, "ph": "X", "name": "ReadAsync 511", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078874455, "dur": 191, "ph": "X", "name": "ReadAsync 368", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078874649, "dur": 191, "ph": "X", "name": "ReadAsync 526", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078874847, "dur": 216, "ph": "X", "name": "ReadAsync 296", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078875066, "dur": 129, "ph": "X", "name": "ReadAsync 430", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078875197, "dur": 24, "ph": "X", "name": "ReadAsync 272", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078875224, "dur": 278, "ph": "X", "name": "ReadAsync 430", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078875508, "dur": 245, "ph": "X", "name": "ReadAsync 296", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078875754, "dur": 4, "ph": "X", "name": "ProcessMessages 448", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078875809, "dur": 170, "ph": "X", "name": "ReadAsync 448", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078875982, "dur": 246, "ph": "X", "name": "ReadAsync 732", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078876261, "dur": 9, "ph": "X", "name": "ProcessMessages 774", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078876271, "dur": 64, "ph": "X", "name": "ReadAsync 774", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078876337, "dur": 34, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078876373, "dur": 243, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078876619, "dur": 437, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078877067, "dur": 193, "ph": "X", "name": "ReadAsync 481", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078877283, "dur": 1, "ph": "X", "name": "ProcessMessages 1196", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078877285, "dur": 69, "ph": "X", "name": "ReadAsync 1196", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078877464, "dur": 18, "ph": "X", "name": "ReadAsync 916", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078877484, "dur": 96, "ph": "X", "name": "ReadAsync 95", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078877583, "dur": 145, "ph": "X", "name": "ReadAsync 613", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078877736, "dur": 76, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078877814, "dur": 213, "ph": "X", "name": "ReadAsync 568", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078878029, "dur": 162, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078878195, "dur": 178, "ph": "X", "name": "ReadAsync 514", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078878375, "dur": 65, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078878444, "dur": 209, "ph": "X", "name": "ReadAsync 484", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078878655, "dur": 158, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078878815, "dur": 177, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078878994, "dur": 58, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078879055, "dur": 153, "ph": "X", "name": "ReadAsync 433", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078879210, "dur": 168, "ph": "X", "name": "ReadAsync 281", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078879381, "dur": 173, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078879556, "dur": 38, "ph": "X", "name": "ReadAsync 502", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078879597, "dur": 21, "ph": "X", "name": "ReadAsync 721", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078879620, "dur": 165, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078879787, "dur": 68, "ph": "X", "name": "ReadAsync 293", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078879856, "dur": 142, "ph": "X", "name": "ReadAsync 466", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078880001, "dur": 40, "ph": "X", "name": "ReadAsync 1078", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078880042, "dur": 1, "ph": "X", "name": "ProcessMessages 973", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078880044, "dur": 85, "ph": "X", "name": "ReadAsync 973", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078880131, "dur": 137, "ph": "X", "name": "ReadAsync 463", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078880270, "dur": 70, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078880347, "dur": 213, "ph": "X", "name": "ReadAsync 487", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078880561, "dur": 1, "ph": "X", "name": "ProcessMessages 1547", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078880563, "dur": 36, "ph": "X", "name": "ReadAsync 1547", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078880601, "dur": 174, "ph": "X", "name": "ReadAsync 672", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078880777, "dur": 202, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078880981, "dur": 4, "ph": "X", "name": "ProcessMessages 308", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078880987, "dur": 51, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078881040, "dur": 200, "ph": "X", "name": "ReadAsync 502", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078881242, "dur": 27, "ph": "X", "name": "ReadAsync 1032", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078881271, "dur": 36, "ph": "X", "name": "ReadAsync 867", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078881309, "dur": 121, "ph": "X", "name": "ReadAsync 299", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078881432, "dur": 168, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078881602, "dur": 199, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078881803, "dur": 99, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078881906, "dur": 134, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078882045, "dur": 140, "ph": "X", "name": "ReadAsync 526", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078882210, "dur": 1, "ph": "X", "name": "ProcessMessages 1833", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078882212, "dur": 31, "ph": "X", "name": "ReadAsync 1833", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078882244, "dur": 1, "ph": "X", "name": "ProcessMessages 861", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078882246, "dur": 139, "ph": "X", "name": "ReadAsync 861", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078882387, "dur": 81, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078882470, "dur": 166, "ph": "X", "name": "ReadAsync 481", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078882637, "dur": 6, "ph": "X", "name": "ProcessMessages 317", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078882685, "dur": 57, "ph": "X", "name": "ReadAsync 317", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078882745, "dur": 121, "ph": "X", "name": "ReadAsync 499", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078882868, "dur": 135, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078883005, "dur": 23, "ph": "X", "name": "ReadAsync 741", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078883037, "dur": 192, "ph": "X", "name": "ReadAsync 242", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078883232, "dur": 102, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078883337, "dur": 108, "ph": "X", "name": "ReadAsync 514", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078883448, "dur": 113, "ph": "X", "name": "ReadAsync 341", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078883565, "dur": 142, "ph": "X", "name": "ReadAsync 556", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078883709, "dur": 167, "ph": "X", "name": "ReadAsync 906", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078883877, "dur": 52, "ph": "X", "name": "ProcessMessages 338", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078883930, "dur": 59, "ph": "X", "name": "ReadAsync 338", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078883991, "dur": 100, "ph": "X", "name": "ReadAsync 517", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078884093, "dur": 82, "ph": "X", "name": "ReadAsync 317", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078884182, "dur": 285, "ph": "X", "name": "ReadAsync 550", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078884469, "dur": 138, "ph": "X", "name": "ReadAsync 371", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078884610, "dur": 171, "ph": "X", "name": "ReadAsync 595", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078884812, "dur": 51, "ph": "X", "name": "ReadAsync 362", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078884865, "dur": 191, "ph": "X", "name": "ReadAsync 580", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078885058, "dur": 131, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078885191, "dur": 303, "ph": "X", "name": "ReadAsync 595", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078885499, "dur": 134, "ph": "X", "name": "ReadAsync 377", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078885636, "dur": 130, "ph": "X", "name": "ReadAsync 550", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078885768, "dur": 146, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078885921, "dur": 171, "ph": "X", "name": "ReadAsync 511", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078886094, "dur": 1, "ph": "X", "name": "ProcessMessages 858", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078886100, "dur": 243, "ph": "X", "name": "ReadAsync 858", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078886345, "dur": 1, "ph": "X", "name": "ProcessMessages 840", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078886346, "dur": 200, "ph": "X", "name": "ReadAsync 840", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078886550, "dur": 68, "ph": "X", "name": "ReadAsync 338", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078886620, "dur": 146, "ph": "X", "name": "ReadAsync 556", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078886768, "dur": 176, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078886947, "dur": 231, "ph": "X", "name": "ReadAsync 592", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078887179, "dur": 32, "ph": "X", "name": "ReadAsync 374", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078887219, "dur": 211, "ph": "X", "name": "ReadAsync 577", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078887448, "dur": 68, "ph": "X", "name": "ReadAsync 768", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078887518, "dur": 136, "ph": "X", "name": "ReadAsync 42", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078887657, "dur": 62, "ph": "X", "name": "ReadAsync 266", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078887725, "dur": 4, "ph": "X", "name": "ProcessMessages 705", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078887732, "dur": 130, "ph": "X", "name": "ReadAsync 705", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078887864, "dur": 6, "ph": "X", "name": "ProcessMessages 1252", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078887875, "dur": 178, "ph": "X", "name": "ReadAsync 1252", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078888056, "dur": 60, "ph": "X", "name": "ReadAsync 359", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078888118, "dur": 345, "ph": "X", "name": "ReadAsync 586", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078888486, "dur": 3, "ph": "X", "name": "ProcessMessages 903", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078888518, "dur": 174, "ph": "X", "name": "ReadAsync 903", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078888695, "dur": 1, "ph": "X", "name": "ProcessMessages 828", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078888699, "dur": 222, "ph": "X", "name": "ReadAsync 828", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078888924, "dur": 66, "ph": "X", "name": "ReadAsync 344", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078888993, "dur": 129, "ph": "X", "name": "ReadAsync 490", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078889164, "dur": 1, "ph": "X", "name": "ProcessMessages 969", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078889166, "dur": 33, "ph": "X", "name": "ReadAsync 969", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078889201, "dur": 1917, "ph": "X", "name": "ReadAsync 893", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078891120, "dur": 1, "ph": "X", "name": "ProcessMessages 2364", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078891122, "dur": 962, "ph": "X", "name": "ReadAsync 2364", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078892103, "dur": 66, "ph": "X", "name": "ProcessMessages 508", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078892210, "dur": 181, "ph": "X", "name": "ReadAsync 508", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078892395, "dur": 1, "ph": "X", "name": "ProcessMessages 353", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078892398, "dur": 185, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078892584, "dur": 1, "ph": "X", "name": "ProcessMessages 1103", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078892586, "dur": 4044, "ph": "X", "name": "ReadAsync 1103", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078896635, "dur": 6, "ph": "X", "name": "ProcessMessages 8163", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078896642, "dur": 77, "ph": "X", "name": "ReadAsync 8163", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078896721, "dur": 1, "ph": "X", "name": "ProcessMessages 784", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078896723, "dur": 356, "ph": "X", "name": "ReadAsync 784", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078897115, "dur": 5375, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078902494, "dur": 14, "ph": "X", "name": "ProcessMessages 4352", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078902509, "dur": 53, "ph": "X", "name": "ReadAsync 4352", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078902564, "dur": 42, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078902609, "dur": 37, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078902649, "dur": 126, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078902778, "dur": 35, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078902928, "dur": 38, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078902969, "dur": 84, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078903055, "dur": 68, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078903123, "dur": 26, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078903151, "dur": 30, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078903183, "dur": 178, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078903362, "dur": 1, "ph": "X", "name": "ProcessMessages 272", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078903364, "dur": 74, "ph": "X", "name": "ReadAsync 272", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078903441, "dur": 44, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078903486, "dur": 54, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078903543, "dur": 23, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078903568, "dur": 69, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078903639, "dur": 183, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078903830, "dur": 105, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078903937, "dur": 74, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078904014, "dur": 21, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078904037, "dur": 48, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078904087, "dur": 77, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078904172, "dur": 19, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078904193, "dur": 107, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078904302, "dur": 35, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078904340, "dur": 224, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078904565, "dur": 1, "ph": "X", "name": "ProcessMessages 208", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078904567, "dur": 92, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078904661, "dur": 5, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078904667, "dur": 69, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078904737, "dur": 135, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078904874, "dur": 342, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078905220, "dur": 14462, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078919688, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078919691, "dur": 995, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078920689, "dur": 2041, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078922734, "dur": 148, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078922884, "dur": 93, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078922980, "dur": 3953, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078926938, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078926941, "dur": 41226, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078968174, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078968177, "dur": 204, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078968386, "dur": 119, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078968508, "dur": 1923, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078970435, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078970437, "dur": 693, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078971133, "dur": 330, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078971470, "dur": 3, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078971475, "dur": 533, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078972010, "dur": 356, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078972370, "dur": 248, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078972620, "dur": 279, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078972902, "dur": 150, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078973054, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078973056, "dur": 181, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078973239, "dur": 224, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078973465, "dur": 435, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078973903, "dur": 174, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078974081, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078974085, "dur": 224, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078974312, "dur": 175, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078974489, "dur": 197, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078974689, "dur": 248, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078974940, "dur": 53, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078974995, "dur": 151, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078975149, "dur": 277, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078975428, "dur": 234, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078975666, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078975669, "dur": 285, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078975957, "dur": 126, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078976088, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078976091, "dur": 148, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078976243, "dur": 491, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078976737, "dur": 294, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078977038, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078977042, "dur": 96, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078977140, "dur": 105, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078977247, "dur": 134, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078977384, "dur": 82, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078977476, "dur": 285, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078977765, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078977768, "dur": 441, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078978210, "dur": 4, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078978215, "dur": 185, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078978405, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078978409, "dur": 322, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078978732, "dur": 5, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078978738, "dur": 32, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078978772, "dur": 159, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078978934, "dur": 268, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078979204, "dur": 98, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078979305, "dur": 76, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078979382, "dur": 239, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078979623, "dur": 352, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078979977, "dur": 188, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078980167, "dur": 51, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078980221, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078980223, "dur": 172, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078980398, "dur": 121, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078980521, "dur": 113, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078980636, "dur": 164, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078980803, "dur": 78, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078980883, "dur": 188, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078981073, "dur": 464, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078981540, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078981542, "dur": 594, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078982138, "dur": 2, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078982141, "dur": 345, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078982488, "dur": 122, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078982612, "dur": 460, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078983075, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078983078, "dur": 598, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078983680, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078983682, "dur": 45, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078983730, "dur": 666, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078984402, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078984405, "dur": 439, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078984846, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078984848, "dur": 984, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078985835, "dur": 1235, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078987074, "dur": 1123, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078988206, "dur": 3, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078988211, "dur": 792, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078989009, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078989013, "dur": 506, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078989524, "dur": 455, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078989983, "dur": 191, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078990176, "dur": 124, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078990307, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078990310, "dur": 98, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078990411, "dur": 45, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078990458, "dur": 82, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078990542, "dur": 176, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078990721, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078990724, "dur": 85, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078990811, "dur": 204, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078991017, "dur": 592, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078991614, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078991617, "dur": 643, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078992265, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078992268, "dur": 75, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078992346, "dur": 1997, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078994354, "dur": 4, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078994361, "dur": 345, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078994708, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078994711, "dur": 277, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078994991, "dur": 178, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078995174, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078995178, "dur": 584, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078995764, "dur": 313, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078996080, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078996083, "dur": 57, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078996143, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078996146, "dur": 254, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078996402, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078996405, "dur": 242, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078996650, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078996652, "dur": 322, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078996976, "dur": 156, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078997136, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078997138, "dur": 273, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078997413, "dur": 327, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078997743, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078997746, "dur": 114, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078997862, "dur": 8, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078997871, "dur": 375, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078998250, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078998252, "dur": 251, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078998538, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078998540, "dur": 42, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078998585, "dur": 291, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078998879, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078998882, "dur": 402, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078999286, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533078999288, "dur": 102047, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533079101342, "dur": 2, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533079101346, "dur": 111, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533079101459, "dur": 81, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533079101604, "dur": 50, "ph": "X", "name": "ReadAsync 7477", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533079101656, "dur": 55, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533079101713, "dur": 79, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533079101793, "dur": 9, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533079101803, "dur": 192, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533079102003, "dur": 138, "ph": "X", "name": "ProcessMessages 2800", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533079102144, "dur": 6541, "ph": "X", "name": "ReadAsync 2800", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533079108691, "dur": 2, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533079108695, "dur": 247, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533079108945, "dur": 2054, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533079111003, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533079111005, "dur": 1230, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533079112239, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533079112240, "dur": 2461, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533079114755, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533079114759, "dur": 582, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533079115344, "dur": 1291, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533079116638, "dur": 3, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533079116642, "dur": 2993, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533079119640, "dur": 1, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533079119652, "dur": 98, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533079119752, "dur": 968, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533079120738, "dur": 3, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533079120743, "dur": 1907, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533079122654, "dur": 1107, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533079123768, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533079123772, "dur": 1693, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533079125477, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533079125480, "dur": 1908, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533079127401, "dur": 5, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533079127407, "dur": 1795, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533079129214, "dur": 4, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533079129220, "dur": 1240, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533079130464, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533079130465, "dur": 329, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533079130796, "dur": 913, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533079131715, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533079131718, "dur": 583, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533079132306, "dur": 1818, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533079134174, "dur": 4, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533079134180, "dur": 1380, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533079135570, "dur": 4, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533079135576, "dur": 3017, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533079138603, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533079138607, "dur": 992, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533079139604, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533079139606, "dur": 98, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533079139707, "dur": 4030, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533079143745, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533079143748, "dur": 92, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533079143843, "dur": 831, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533079144678, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533079144679, "dur": 551, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533079145238, "dur": 3, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533079145242, "dur": 479, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533079145725, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533079145726, "dur": 89, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533079145817, "dur": 375, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533079146196, "dur": 439, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533079146657, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533079146659, "dur": 90, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533079146753, "dur": 2, "ph": "X", "name": "ProcessMessages 28", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533079146757, "dur": 124, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533079146884, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533079146887, "dur": 343, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533079147234, "dur": 212, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533079147451, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533079147455, "dur": 139, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533079147596, "dur": 145, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533079147744, "dur": 240, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533079147988, "dur": 2, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533079147991, "dur": 275, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533079148269, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533079148270, "dur": 199, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533079148474, "dur": 2, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533079148478, "dur": 82, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533079148563, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533079148565, "dur": 50, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533079148620, "dur": 57, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533079148681, "dur": 194, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533079148878, "dur": 15, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533079148894, "dur": 156, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533079149054, "dur": 62, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533079149118, "dur": 228, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533079149350, "dur": 24, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533079149377, "dur": 66, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533079149445, "dur": 172, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533079149620, "dur": 103, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533079149725, "dur": 70, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533079149797, "dur": 162, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533079149963, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533079149967, "dur": 78, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533079150048, "dur": 58, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533079150108, "dur": 93, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533079150204, "dur": 114, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533079150322, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533079150325, "dur": 50, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533079150377, "dur": 64, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533079150444, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533079150447, "dur": 111, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533079150561, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533079150564, "dur": 103, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533079150670, "dur": 66, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533079150738, "dur": 97, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533079150837, "dur": 712, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533079151557, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533079151577, "dur": 59, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533079151639, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533079151641, "dur": 345567, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533079497215, "dur": 29, "ph": "X", "name": "ProcessMessages 1854", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533079497246, "dur": 1193, "ph": "X", "name": "ReadAsync 1854", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533079498441, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533079498443, "dur": 1552, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533079499997, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533079499998, "dur": 161693, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533079661701, "dur": 18, "ph": "X", "name": "ProcessMessages 381", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533079661720, "dur": 2866, "ph": "X", "name": "ReadAsync 381", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533079664591, "dur": 3, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533079664594, "dur": 104959, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533079769572, "dur": 3, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533079769577, "dur": 104, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533079769684, "dur": 69, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533079769755, "dur": 77, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533079769835, "dur": 79, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533079769922, "dur": 121, "ph": "X", "name": "ProcessMessages 5339", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533079770046, "dur": 3486, "ph": "X", "name": "ReadAsync 5339", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533079773544, "dur": 6, "ph": "X", "name": "ProcessMessages 40", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533079773552, "dur": 786, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533079774345, "dur": 25, "ph": "X", "name": "ProcessMessages 92", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533079774371, "dur": 48559, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533079822952, "dur": 2, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533079822956, "dur": 81, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533079823039, "dur": 38, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533079823079, "dur": 121, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533079823215, "dur": 58, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533079823277, "dur": 21, "ph": "X", "name": "ProcessMessages 7965", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533079823308, "dur": 3238, "ph": "X", "name": "ReadAsync 7965", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533079826552, "dur": 3, "ph": "X", "name": "ProcessMessages 40", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533079826557, "dur": 479, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533079827038, "dur": 38, "ph": "X", "name": "ProcessMessages 108", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533079827077, "dur": 237, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533079827315, "dur": 1, "ph": "X", "name": "ProcessMessages 25", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748533079827318, "dur": 5962, "ph": "X", "name": "ReadAsync 25", "args": {}}, {"pid": 27554, "tid": 6381, "ts": 1748533079835570, "dur": 1920, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 27554, "tid": 21474836480, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 27554, "tid": 21474836480, "ts": 1748533078779992, "dur": 100883, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 27554, "tid": 21474836480, "ts": 1748533078880877, "dur": 50, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 27554, "tid": 6381, "ts": 1748533079837493, "dur": 10, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 27554, "tid": 17179869184, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 27554, "tid": 17179869184, "ts": 1748533078750523, "dur": 1083603, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 27554, "tid": 17179869184, "ts": 1748533078751007, "dur": 28670, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 27554, "tid": 17179869184, "ts": 1748533079834136, "dur": 190, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 27554, "tid": 17179869184, "ts": 1748533079834178, "dur": 77, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 27554, "tid": 6381, "ts": 1748533079837505, "dur": 6, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1748533078800327, "dur": 2432, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748533078802766, "dur": 25081, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748533078827961, "dur": 197, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1748533078828159, "dur": 117, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748533078829879, "dur": 71, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_F4C05CFCBFAEF91C.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748533078830152, "dur": 658, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748533078836197, "dur": 162, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Utilities.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1748533078836532, "dur": 737, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.State.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1748533078837356, "dur": 773, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1748533078840858, "dur": 4785, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1748533078889518, "dur": 78, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.Shared.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1748533078892876, "dur": 100, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1748533078893201, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/FMODUnityResonanceEditor.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1748533078893321, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/FMODUnityResonanceEditor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1748533078828327, "dur": 65634, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748533078893969, "dur": 933764, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748533079827826, "dur": 51, "ph": "X", "name": "BuildQueueDestroyTail", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748533079827906, "dur": 1110, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1748533078828324, "dur": 66152, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748533078894478, "dur": 3020, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.BuildProfileModule.dll_5A44DDE874DD43A2.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748533078897505, "dur": 2291, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.MultiplayerModule.dll_F9A822B4784E7167.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748533078899797, "dur": 150, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748533078900130, "dur": 610, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_557EEB5E99C128BC.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748533078900740, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748533078900807, "dur": 104, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_9927031187632C7E.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748533078900911, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748533078901023, "dur": 98, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_8D74880C622CE1AD.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748533078901180, "dur": 90, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_4E33C3C55AB9064E.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748533078901312, "dur": 130, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_DAACB4F719294F92.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748533078901443, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748533078901509, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_C20199F83674ABBE.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748533078901619, "dur": 213, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_90AD9150DA4AFB38.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748533078901832, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748533078901893, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.NVIDIAModule.dll_8C003B92E4EC36BD.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748533078902009, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748533078902148, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748533078902471, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748533078902523, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748533078902720, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748533078902842, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748533078903084, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748533078903185, "dur": 142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748533078903330, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748533078903470, "dur": 226, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748533078903718, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748533078903852, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748533078904049, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748533078904120, "dur": 147, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748533078904276, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748533078904379, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748533078904504, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748533078904640, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748533078904707, "dur": 137, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748533078904853, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748533078904966, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748533078905083, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748533078905199, "dur": 145, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748533078905409, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748533078905541, "dur": 1483, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748533078907024, "dur": 1019, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748533078908043, "dur": 1090, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748533078909134, "dur": 755, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748533078909890, "dur": 897, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748533078910787, "dur": 755, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748533078911542, "dur": 853, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748533078912395, "dur": 932, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748533078913327, "dur": 909, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748533078914236, "dur": 724, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748533078914960, "dur": 814, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748533078915774, "dur": 1075, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748533078916849, "dur": 1196, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748533078918847, "dur": 758, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.shadergraph@afc42d8f5d23/Editor/Data/Nodes/Artistic/Blend/BlendNode.cs"}}, {"pid": 12345, "tid": 1, "ts": 1748533078918045, "dur": 2605, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748533078920650, "dur": 1439, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748533078922089, "dur": 1365, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748533078923569, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748533078923646, "dur": 1018, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748533078924665, "dur": 1424, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748533078926090, "dur": 1597, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748533078927687, "dur": 916, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748533078928603, "dur": 851, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748533078929454, "dur": 895, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748533078930349, "dur": 1002, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748533078931352, "dur": 971, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748533078932323, "dur": 877, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748533078933200, "dur": 851, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748533078934051, "dur": 845, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748533078934896, "dur": 1786, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748533078936682, "dur": 2191, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748533078938873, "dur": 2116, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748533078940989, "dur": 3125, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748533078944115, "dur": 2058, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748533078946173, "dur": 2669, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748533078948844, "dur": 2191, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748533078951036, "dur": 1361, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748533078952398, "dur": 1414, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748533078953813, "dur": 1172, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748533078954985, "dur": 1173, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748533078956912, "dur": 1697, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.mathematics@8017b507cc74/Unity.Mathematics/bool4x2.gen.cs"}}, {"pid": 12345, "tid": 1, "ts": 1748533078958610, "dur": 863, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.mathematics@8017b507cc74/Unity.Mathematics/bool4.gen.cs"}}, {"pid": 12345, "tid": 1, "ts": 1748533078959662, "dur": 4742, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.mathematics@8017b507cc74/Unity.Mathematics/bool3x2.gen.cs"}}, {"pid": 12345, "tid": 1, "ts": 1748533078956159, "dur": 8409, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748533078964696, "dur": 2795, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.collab-proxy@50ac96531b63/Editor/Views/CreateWorkspace/Dialogs/RepositoriesListView.cs"}}, {"pid": 12345, "tid": 1, "ts": 1748533078964569, "dur": 3727, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748533078968297, "dur": 1101, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748533078969398, "dur": 1222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748533078970620, "dur": 631, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748533078971251, "dur": 483, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748533078971736, "dur": 2297, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748533078974034, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748533078974136, "dur": 401, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748533078974538, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748533078974693, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748533078974775, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748533078974864, "dur": 3152, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748533078978017, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748533078978124, "dur": 431, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748533078978589, "dur": 295, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748533078978885, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748533078979005, "dur": 2938, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 1, "ts": 1748533078982003, "dur": 306, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748533079101835, "dur": 708, "ph": "X", "name": "EmitNodeFinish", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748533078982889, "dur": 119668, "ph": "X", "name": "ILPP-Configuration", "args": {"detail": "Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 1, "ts": 1748533079103742, "dur": 1888, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Universal.2D.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748533079105631, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748533079105758, "dur": 2593, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748533079108352, "dur": 1010, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748533079109413, "dur": 5972, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.State.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748533079115387, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748533079115521, "dur": 4720, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748533079120242, "dur": 216, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748533079120491, "dur": 3872, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748533079124363, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748533079124463, "dur": 3977, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/FMODUnityResonanceEditor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748533079128441, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748533079128570, "dur": 3891, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748533079132461, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748533079132600, "dur": 5121, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.AI.Navigation.Updater.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748533079137724, "dur": 192, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748533079137928, "dur": 9331, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/PPv2URPConverters.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748533079147260, "dur": 261, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748533079147529, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748533079147633, "dur": 107, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll"}}, {"pid": 12345, "tid": 1, "ts": 1748533079147840, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Universal.Editor.dll"}}, {"pid": 12345, "tid": 1, "ts": 1748533079147891, "dur": 176, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748533079148133, "dur": 230, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748533079148367, "dur": 105, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.PerformanceTesting.Editor.pdb"}}, {"pid": 12345, "tid": 1, "ts": 1748533079148472, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748533079148570, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Collections.Editor.dll"}}, {"pid": 12345, "tid": 1, "ts": 1748533079148621, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748533079148770, "dur": 167, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748533079148941, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.AI.Navigation.Updater.pdb"}}, {"pid": 12345, "tid": 1, "ts": 1748533079149061, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Cursor.Editor.pdb"}}, {"pid": 12345, "tid": 1, "ts": 1748533079149147, "dur": 176, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748533079149346, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748533079149477, "dur": 203, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748533079149734, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748533079149805, "dur": 100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Core.Editor.Shared.dll"}}, {"pid": 12345, "tid": 1, "ts": 1748533079149906, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748533079150053, "dur": 112, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Collections.pdb"}}, {"pid": 12345, "tid": 1, "ts": 1748533079150166, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748533079150275, "dur": 86, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEditor.UI.dll"}}, {"pid": 12345, "tid": 1, "ts": 1748533079150361, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748533079150479, "dur": 187, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748533079150711, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748533079150840, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748533079151011, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748533079151165, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748533079151269, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748533079151332, "dur": 819, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748533079152152, "dur": 675561, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748533078828295, "dur": 65923, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748533078894221, "dur": 208, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 2, "ts": 1748533078895256, "dur": 1979, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_9CEC3B310263C3AF.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748533078897244, "dur": 1986, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridAndSnapModule.dll_BA86083999E80018.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748533078899230, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748533078899305, "dur": 74, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridAndSnapModule.dll_BA86083999E80018.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748533078899380, "dur": 167, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Physics2DModule.dll_BB7D6B77AADC3636.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748533078899553, "dur": 566, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SafeModeModule.dll_F3E4D7EAC2485D07.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748533078900741, "dur": 99, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ShaderVariantAnalyticsModule.dll_50D38B6F9A6D24D8.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748533078900841, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748533078900899, "dur": 149, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_7B6D90F67FD0A1D6.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748533078901101, "dur": 122, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_796E436A12A0D05D.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748533078901223, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748533078901287, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_FB9A8EFABECD3061.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748533078901350, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748533078901455, "dur": 88, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_FA4CDBD8E90E39CA.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748533078901639, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Antlr3.Runtime.dll_F395896E028284AF.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748533078901720, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748533078901841, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748533078902308, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748533078902443, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748533078902602, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748533078902694, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748533078902762, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748533078902820, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748533078903078, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748533078903164, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748533078903298, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748533078903425, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748533078903551, "dur": 150, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748533078903702, "dur": 101, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Cursor.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 2, "ts": 1748533078903803, "dur": 145, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748533078903987, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748533078904071, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748533078904152, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748533078904266, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748533078904373, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748533078904473, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748533078904575, "dur": 134, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748533078904712, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748533078904823, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748533078904947, "dur": 146, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748533078905120, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748533078905224, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748533078905348, "dur": 67, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp-Editor.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1748533078905416, "dur": 1489, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748533078906905, "dur": 1034, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748533078907939, "dur": 1052, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748533078908992, "dur": 821, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748533078909813, "dur": 893, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748533078910707, "dur": 705, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748533078911412, "dur": 888, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748533078912300, "dur": 922, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748533078913222, "dur": 924, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748533078914147, "dur": 716, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748533078914863, "dur": 831, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748533078915695, "dur": 1051, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748533078916747, "dur": 1038, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748533078918848, "dur": 759, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.shadergraph@afc42d8f5d23/Editor/Data/Nodes/Input/PropertyNode.cs"}}, {"pid": 12345, "tid": 2, "ts": 1748533078917785, "dur": 2243, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748533078920029, "dur": 1873, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748533078921902, "dur": 1356, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748533078923258, "dur": 980, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748533078924238, "dur": 1289, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748533078925527, "dur": 1760, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748533078927287, "dur": 975, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748533078928262, "dur": 928, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748533078929190, "dur": 939, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748533078930129, "dur": 990, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748533078931119, "dur": 953, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748533078932072, "dur": 895, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748533078932967, "dur": 870, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748533078933837, "dur": 814, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748533078934651, "dur": 1592, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748533078936243, "dur": 1846, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748533078938090, "dur": 2239, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748533078940329, "dur": 2450, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748533078942779, "dur": 2738, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748533078945517, "dur": 2287, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748533078947808, "dur": 2543, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748533078950352, "dur": 1640, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748533078951992, "dur": 1173, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748533078953165, "dur": 1401, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748533078954567, "dur": 1075, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748533078955642, "dur": 1191, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748533078956954, "dur": 2485, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.collab-proxy@50ac96531b63/Editor/Views/PendingChanges/Dialogs/LaunchDependenciesDialog.cs"}}, {"pid": 12345, "tid": 2, "ts": 1748533078959777, "dur": 4693, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.collab-proxy@50ac96531b63/Editor/Views/PendingChanges/Dialogs/DependenciesDialog.cs"}}, {"pid": 12345, "tid": 2, "ts": 1748533078964690, "dur": 2758, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.collab-proxy@50ac96531b63/Editor/Views/PendingChanges/Changelists/ChangelistMenu.cs"}}, {"pid": 12345, "tid": 2, "ts": 1748533078956833, "dur": 11072, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748533078967905, "dur": 826, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748533078968731, "dur": 1386, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748533078970117, "dur": 587, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748533078970924, "dur": 286, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748533078971211, "dur": 511, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748533078971724, "dur": 256, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Searcher.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748533078971980, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748533078972061, "dur": 613, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Searcher.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748533078972675, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748533078972816, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748533078972884, "dur": 408, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748533078973292, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748533078973380, "dur": 343, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748533078973804, "dur": 1091, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748533078974896, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748533078975003, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748533078975120, "dur": 4455, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748533078979575, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748533078979697, "dur": 195, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Multiplayer.Center.Common.ref.dll_70C63ADD63245F54.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748533078979892, "dur": 196, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748533078980091, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748533078980213, "dur": 290, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.ForUI.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748533078980576, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748533078980672, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748533078980727, "dur": 1470, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748533078982224, "dur": 323, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748533078982547, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748533078982655, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748533078982730, "dur": 1074, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748533078983804, "dur": 187, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748533078984002, "dur": 5146, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Cursor.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748533078989150, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748533078989273, "dur": 387, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748533078989660, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748533078989750, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/FMODUnityResonanceEditor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748533078989888, "dur": 168, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.PerformanceTesting.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748533078990152, "dur": 2642, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.PerformanceTesting.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748533078992795, "dur": 166, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748533078992969, "dur": 125, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.PerformanceTesting.ref.dll_A9B74A479D61B912.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748533078993094, "dur": 230, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748533078993326, "dur": 2319, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.PerformanceTesting.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748533078995646, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748533078995742, "dur": 487, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.PerformanceTesting.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748533078996230, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748533078996364, "dur": 1930, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748533078998298, "dur": 193, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748533078998513, "dur": 687, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748533078999226, "dur": 104999, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748533079104228, "dur": 3790, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.AI.Navigation.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748533079108019, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748533079108115, "dur": 3510, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Core.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748533079111627, "dur": 167, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748533079111800, "dur": 4159, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.GPUDriven.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748533079115960, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748533079116082, "dur": 4521, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/FMODUnity.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748533079120604, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748533079120715, "dur": 2269, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.ShaderGraph.Utilities.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748533079122985, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748533079123108, "dur": 4912, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.InputSystem.DocCodeSamples.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748533079128022, "dur": 386, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748533079128427, "dur": 6313, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.State.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748533079134741, "dur": 156, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748533079134905, "dur": 7895, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.Flow.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748533079142802, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748533079142915, "dur": 9168, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.CollabProxy.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748533079152149, "dur": 675505, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748533078828330, "dur": 66209, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748533078894540, "dur": 2634, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_576EED2B1C54EF9B.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748533078897180, "dur": 2495, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GIModule.dll_CD898CC368A4D4E9.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748533078899736, "dur": 332, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_94E7D34F0A24E8EE.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748533078900110, "dur": 577, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UmbraModule.dll_6AD82FAF92A13AC6.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748533078900702, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_3EDBDDC0989E0EE4.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748533078900795, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748533078900864, "dur": 134, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_B5FD6E098A39A5D2.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748533078900998, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748533078901061, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_0DC95BA72EA6C3C0.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748533078901148, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748533078901219, "dur": 86, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_42DE6F6D94492967.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748533078901305, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748533078901366, "dur": 121, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_5836CD6851EC9E19.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748533078901487, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748533078901643, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Newtonsoft.Json.dll_8BB13F70623D482C.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748533078901731, "dur": 106, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Antlr3.Runtime.dll_9790B5B5B9BAF4F9.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748533078901838, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748533078901985, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Unsafe.dll_C5A9EDBF39CB7C6A.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748533078902065, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748533078902212, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Mdb.dll_1C1CB7F65D73ACD6.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748533078902312, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748533078902410, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748533078902524, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748533078902581, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748533078902736, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748533078903089, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748533078903201, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748533078903311, "dur": 138, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748533078903482, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748533078903587, "dur": 158, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748533078903824, "dur": 138, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748533078903981, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748533078904065, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748533078904123, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748533078904251, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748533078904364, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748533078904479, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748533078904567, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748533078904688, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748533078904794, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748533078904925, "dur": 137, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748533078905066, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748533078905155, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748533078905319, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748533078905441, "dur": 1446, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748533078906888, "dur": 1089, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748533078907977, "dur": 1027, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748533078909004, "dur": 839, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748533078909843, "dur": 902, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748533078910745, "dur": 631, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748533078911376, "dur": 814, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748533078912190, "dur": 916, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748533078913106, "dur": 966, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748533078914072, "dur": 705, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748533078914777, "dur": 800, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748533078915577, "dur": 1082, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748533078916659, "dur": 889, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748533078918846, "dur": 757, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.shadergraph@afc42d8f5d23/Editor/Data/Nodes/Input/Texture/SampleTexture2DNode.cs"}}, {"pid": 12345, "tid": 3, "ts": 1748533078917548, "dur": 2305, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748533078919854, "dur": 1913, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748533078921768, "dur": 1446, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748533078923215, "dur": 1009, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748533078924224, "dur": 1219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748533078925444, "dur": 1771, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748533078927216, "dur": 993, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748533078928209, "dur": 898, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748533078929107, "dur": 908, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748533078930015, "dur": 1017, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748533078931032, "dur": 919, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748533078931951, "dur": 911, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748533078932863, "dur": 865, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748533078933728, "dur": 819, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748533078934547, "dur": 1531, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748533078936078, "dur": 1914, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748533078937992, "dur": 2281, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748533078940274, "dur": 2383, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748533078942658, "dur": 2772, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748533078945430, "dur": 2331, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748533078947763, "dur": 2627, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748533078950390, "dur": 1606, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748533078951996, "dur": 1156, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748533078953152, "dur": 1421, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748533078954574, "dur": 1071, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748533078955645, "dur": 1179, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748533078956940, "dur": 2448, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.collab-proxy@50ac96531b63/Editor/Views/Shelves/ShelvesSelection.cs"}}, {"pid": 12345, "tid": 3, "ts": 1748533078959745, "dur": 4715, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.collab-proxy@50ac96531b63/Editor/Views/ShelvePendingChangesDialog.cs"}}, {"pid": 12345, "tid": 3, "ts": 1748533078964683, "dur": 2751, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.collab-proxy@50ac96531b63/Editor/Views/PendingChanges/PendingChangesViewMenu.cs"}}, {"pid": 12345, "tid": 3, "ts": 1748533078956824, "dur": 11074, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748533078967898, "dur": 818, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748533078968716, "dur": 1402, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748533078970118, "dur": 661, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748533078970909, "dur": 290, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748533078971199, "dur": 518, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748533078971718, "dur": 390, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748533078972109, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748533078972171, "dur": 4274, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748533078976445, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748533078976599, "dur": 138, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748533078976789, "dur": 816, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748533078977605, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748533078977741, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748533078977847, "dur": 3227, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748533078981074, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748533078981173, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748533078981247, "dur": 452, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748533078981723, "dur": 1089, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748533078982813, "dur": 168, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748533078983027, "dur": 88, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748533078983141, "dur": 509, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748533078983685, "dur": 610, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748533078984295, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748533078984444, "dur": 2051, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Rendering.LightTransport.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748533078986496, "dur": 141, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748533078986643, "dur": 5587, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.ForUI.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748533078992230, "dur": 233, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748533078992481, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748533078992547, "dur": 4154, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748533078996701, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748533078996802, "dur": 1508, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748533078998310, "dur": 105441, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748533079103754, "dur": 3564, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Timeline.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748533079107355, "dur": 5386, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748533079112742, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748533079112859, "dur": 4445, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.Flow.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748533079117305, "dur": 293, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748533079117604, "dur": 9185, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Collections.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748533079126791, "dur": 852, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748533079127665, "dur": 4621, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748533079132287, "dur": 169, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748533079132469, "dur": 3448, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Cursor.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748533079135919, "dur": 584, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748533079136523, "dur": 3539, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.PlasticSCM.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748533079140063, "dur": 215, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748533079140297, "dur": 11922, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Multiplayer.Center.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748533079152261, "dur": 675409, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748533078828315, "dur": 66141, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748533078895304, "dur": 2192, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_DB357A1C5B5CB80B.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748533078897502, "dur": 1966, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridModule.dll_86EBD7C4DEEB8CFD.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748533078899473, "dur": 529, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PropertiesModule.dll_542089FB00C06F3A.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748533078900052, "dur": 684, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_AAC09D6387BA9514.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748533078900736, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748533078900793, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_10C02A94B188B9C3.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748533078900886, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748533078900996, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_458C519AD2A9288F.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748533078901089, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748533078901162, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UmbraModule.dll_3104EC13C41E6B6B.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748533078901242, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748533078901303, "dur": 116, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_2AF15A5FC73B3288.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748533078901419, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748533078901494, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_0240B089C917E98F.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748533078901572, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748533078901632, "dur": 89, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Collections.LowLevel.ILSupport.dll_A8DBA71237A1D37F.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748533078901721, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748533078901809, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.TextureAssets.dll_D9468BE09286D288.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748533078901991, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748533078902302, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748533078902709, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748533078902785, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748533078902920, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748533078903109, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748533078903234, "dur": 135, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748533078903382, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748533078903500, "dur": 146, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748533078903675, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748533078903808, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748533078903937, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748533078904038, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748533078904112, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748533078904237, "dur": 150, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748533078904395, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748533078904491, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748533078904634, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748533078904720, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748533078904848, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748533078904954, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748533078905076, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748533078905183, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748533078905308, "dur": 176, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748533078905486, "dur": 1510, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748533078906996, "dur": 1026, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748533078908022, "dur": 1106, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748533078909128, "dur": 759, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748533078909887, "dur": 898, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748533078910785, "dur": 765, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748533078911550, "dur": 840, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748533078912391, "dur": 909, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748533078913300, "dur": 885, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748533078914185, "dur": 687, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748533078914873, "dur": 837, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748533078915711, "dur": 1069, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748533078916781, "dur": 1125, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748533078918854, "dur": 762, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.shadergraph@afc42d8f5d23/Editor/Data/Nodes/FormerNameAttribute.cs"}}, {"pid": 12345, "tid": 4, "ts": 1748533078917906, "dur": 2594, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748533078920501, "dur": 1453, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748533078921954, "dur": 1340, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748533078923294, "dur": 979, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748533078924273, "dur": 1233, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748533078925507, "dur": 1738, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748533078927246, "dur": 985, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748533078928232, "dur": 906, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748533078929138, "dur": 921, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748533078930060, "dur": 1004, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748533078931064, "dur": 923, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748533078931987, "dur": 892, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748533078932879, "dur": 860, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748533078933739, "dur": 832, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748533078934572, "dur": 1581, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748533078936153, "dur": 1844, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748533078937997, "dur": 2197, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748533078940195, "dur": 2354, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748533078942550, "dur": 2847, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748533078945398, "dur": 2265, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748533078947667, "dur": 2637, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748533078950304, "dur": 1667, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748533078951972, "dur": 1133, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748533078953105, "dur": 1374, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748533078954480, "dur": 1080, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748533078955560, "dur": 1199, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748533078956931, "dur": 2456, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.collab-proxy@50ac96531b63/Editor/_Deprecated/WebApi/OrganizationCredentials.cs"}}, {"pid": 12345, "tid": 4, "ts": 1748533078959729, "dur": 4729, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.collab-proxy@50ac96531b63/Editor/_Deprecated/CollabMigration/MigrateCollabProject.cs"}}, {"pid": 12345, "tid": 4, "ts": 1748533078964681, "dur": 2750, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.collab-proxy@50ac96531b63/Editor/WebApi/CredentialsResponse.cs"}}, {"pid": 12345, "tid": 4, "ts": 1748533078956759, "dur": 11069, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748533078967829, "dur": 849, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748533078968678, "dur": 1325, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748533078970003, "dur": 1244, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748533078971247, "dur": 484, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748533078971734, "dur": 1946, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.AI.Navigation.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748533078973680, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748533078973817, "dur": 1296, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.AI.Navigation.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748533078975114, "dur": 173, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748533078975290, "dur": 86, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.AI.Navigation.ref.dll_62FEAEE8B284189E.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748533078975376, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748533078975518, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.AI.Navigation.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748533078975645, "dur": 2478, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.AI.Navigation.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748533078978123, "dur": 157, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748533078978284, "dur": 127, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748533078978454, "dur": 1774, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748533078980229, "dur": 241, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748533078980474, "dur": 496, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748533078981010, "dur": 3884, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748533078984895, "dur": 453, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748533078985376, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748533078985449, "dur": 5009, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748533078990459, "dur": 200, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748533078990668, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748533078990844, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748533078991004, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.RenderPipelines.Core.ShaderLibrary.ref.dll_8EDD8A7C38E993CC.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748533078991063, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748533078991315, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.TestFramework.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748533078991419, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748533078991479, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748533078991540, "dur": 249, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.DocCodeSamples.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748533078991821, "dur": 4724, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.TestFramework.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748533078996546, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748533078996627, "dur": 1678, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748533078998306, "dur": 1193, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748533078999502, "dur": 241, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748533078999766, "dur": 105892, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748533079105669, "dur": 10930, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Multiplayer.Center.Common.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748533079116601, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748533079116696, "dur": 2003, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748533079118700, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748533079118816, "dur": 2572, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Config.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748533079121389, "dur": 693, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748533079122092, "dur": 7657, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748533079129751, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748533079129875, "dur": 4101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.SettingsProvider.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748533079133977, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748533079134052, "dur": 4529, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748533079138612, "dur": 183, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748533079138812, "dur": 5566, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.TextMeshPro.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748533079144379, "dur": 204, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748533079144593, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748533079144655, "dur": 389, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748533079145066, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748533079145147, "dur": 198, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748533079145352, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748533079145411, "dur": 187, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748533079145612, "dur": 527, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748533079146149, "dur": 185, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748533079146373, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748533079146495, "dur": 86, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.TextMeshPro.dll"}}, {"pid": 12345, "tid": 4, "ts": 1748533079146581, "dur": 135, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748533079146737, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.PerformanceTesting.dll"}}, {"pid": 12345, "tid": 4, "ts": 1748533079146816, "dur": 146, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748533079146970, "dur": 291, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748533079147266, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748533079147354, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Mathematics.dll"}}, {"pid": 12345, "tid": 4, "ts": 1748533079147407, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748533079147478, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Core.dll"}}, {"pid": 12345, "tid": 4, "ts": 1748533079147549, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748533079147662, "dur": 144, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Core.dll"}}, {"pid": 12345, "tid": 4, "ts": 1748533079147807, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/PPv2URPConverters.pdb"}}, {"pid": 12345, "tid": 4, "ts": 1748533079147871, "dur": 138, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748533079148024, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.TextMeshPro.Editor.pdb"}}, {"pid": 12345, "tid": 4, "ts": 1748533079148077, "dur": 165, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748533079148246, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748533079148374, "dur": 155, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748533079148577, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748533079148681, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Core.Editor.dll"}}, {"pid": 12345, "tid": 4, "ts": 1748533079148745, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748533079148880, "dur": 110, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.AI.Navigation.Updater.dll"}}, {"pid": 12345, "tid": 4, "ts": 1748533079148992, "dur": 155, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748533079149166, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748533079149273, "dur": 142, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.SettingsProvider.Editor.dll"}}, {"pid": 12345, "tid": 4, "ts": 1748533079149416, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748533079149528, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748533079149590, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Burst.Editor.pdb"}}, {"pid": 12345, "tid": 4, "ts": 1748533079149663, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748533079149763, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Rendering.LightTransport.Editor.pdb"}}, {"pid": 12345, "tid": 4, "ts": 1748533079149817, "dur": 164, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748533079150064, "dur": 131, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.AI.Navigation.Editor.ConversionSystem.dll"}}, {"pid": 12345, "tid": 4, "ts": 1748533079150195, "dur": 140, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748533079150472, "dur": 160, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748533079150676, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748533079150807, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748533079150956, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748533079151035, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748533079151094, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Mathematics.pdb"}}, {"pid": 12345, "tid": 4, "ts": 1748533079151153, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748533079151233, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Timeline.pdb"}}, {"pid": 12345, "tid": 4, "ts": 1748533079151302, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748533079151395, "dur": 347159, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748533079498763, "dur": 69, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 4, "ts": 1748533079498555, "dur": 1403, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748533079500444, "dur": 192, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748533079769838, "dur": 726, "ph": "X", "name": "EmitNodeFinish", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748533079500980, "dur": 269637, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/200b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748533079773991, "dur": 77, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library/Bee/artifacts/200b0aE.dag/post-processed/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 4, "ts": 1748533079773983, "dur": 86, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 4, "ts": 1748533079774092, "dur": 820, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 4, "ts": 1748533079774917, "dur": 52735, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748533078828328, "dur": 66200, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748533078894530, "dur": 2621, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_A97ABBD6C696F706.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748533078897162, "dur": 2519, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EmbreeModule.dll_33EF23AB874F1C0F.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748533078899749, "dur": 410, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SketchUpModule.dll_DBF9BD3B0D79F91E.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748533078900207, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_73BBD5453873209A.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748533078900273, "dur": 500, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_B6303FFB36F7BFF8.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748533078900773, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748533078900839, "dur": 99, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_D95259C03EE6A7C4.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748533078900938, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748533078901043, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_D4B8FF3D259E96C1.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748533078901130, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748533078901204, "dur": 91, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_43500FCF65A89BF3.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748533078901343, "dur": 125, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_90196DBEEEA0E95D.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748533078901468, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748533078901549, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_42EA6A51FFD2E09A.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748533078901621, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748533078901700, "dur": 3741, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748533078905485, "dur": 17679, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748533078923165, "dur": 179, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748533078923356, "dur": 113, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TestRunner.ref.dll_AA3B5DF2776E59D2.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748533078923521, "dur": 3894, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748533078927415, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748533078927518, "dur": 41077, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748533078968598, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748533078968752, "dur": 162, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TestRunner.ref.dll_DE1D13444BAAFBE7.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748533078968914, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748533078968998, "dur": 1913, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748533078970962, "dur": 628, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748533078971647, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748533078971763, "dur": 1228, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748533078972992, "dur": 176, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748533078973180, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748533078973251, "dur": 674, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748533078973977, "dur": 2105, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748533078976082, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748533078976184, "dur": 2150, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748533078978336, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748533078978400, "dur": 869, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748533078979269, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748533078979451, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748533078979512, "dur": 114, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748533078979627, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748533078979710, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748533078979811, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/FMODUnityResonance.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748533078979914, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748533078979981, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748533078980047, "dur": 178, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.AI.Navigation.Updater.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748533078980251, "dur": 369, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Collections.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748533078980644, "dur": 211, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Rendering.LightTransport.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748533078980901, "dur": 127, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Cursor.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748533078981059, "dur": 200, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748533078981288, "dur": 2102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748533078983391, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748533078983503, "dur": 7424, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748533078990927, "dur": 145, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748533078991128, "dur": 4662, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748533078995791, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748533078995852, "dur": 1368, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748533078997221, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748533078997328, "dur": 180, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/PPv2URPConverters.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748533078997534, "dur": 518, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/PPv2URPConverters.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748533078998075, "dur": 242, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748533078998317, "dur": 105797, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748533079104115, "dur": 5227, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.InputSystem.TestFramework.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748533079109343, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748533079109454, "dur": 3416, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Shaders.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748533079112871, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748533079112967, "dur": 3502, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.InputSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748533079116470, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748533079116584, "dur": 3685, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.InputSystem.ForUI.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748533079120270, "dur": 208, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748533079120479, "dur": 175, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.InputSystem.ForUI.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748533079120657, "dur": 2941, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.AI.Navigation.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748533079123599, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748533079123734, "dur": 2388, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.AI.Navigation.Editor.ConversionSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748533079126122, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748533079126243, "dur": 6724, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.ShaderGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748533079132968, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748533079133076, "dur": 920, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.ShaderGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748533079134000, "dur": 7662, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Timeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748533079141664, "dur": 147, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748533079141834, "dur": 3982, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748533079145818, "dur": 163, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748533079145995, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748533079146118, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Universal.Config.Runtime.dll"}}, {"pid": 12345, "tid": 5, "ts": 1748533079146173, "dur": 185, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748533079146362, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748533079146442, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748533079146658, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748533079146835, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748533079146921, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.AI.Navigation.dll"}}, {"pid": 12345, "tid": 5, "ts": 1748533079146989, "dur": 438, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748533079147472, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Core.Runtime.dll"}}, {"pid": 12345, "tid": 5, "ts": 1748533079147565, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Core.Runtime.Shared.dll"}}, {"pid": 12345, "tid": 5, "ts": 1748533079147636, "dur": 251, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748533079147892, "dur": 124, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Universal.Editor.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1748533079148016, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748533079148149, "dur": 118, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/FMODUnityEditor.dll"}}, {"pid": 12345, "tid": 5, "ts": 1748533079148267, "dur": 161, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748533079148436, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748533079148536, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748533079148688, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748533079148807, "dur": 134, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Mathematics.Editor.dll"}}, {"pid": 12345, "tid": 5, "ts": 1748533079148942, "dur": 169, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748533079149115, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.State.Editor.dll"}}, {"pid": 12345, "tid": 5, "ts": 1748533079149183, "dur": 184, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748533079149374, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.ShaderGraph.Editor.dll"}}, {"pid": 12345, "tid": 5, "ts": 1748533079149436, "dur": 173, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748533079149611, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Searcher.Editor.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1748533079149694, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748533079149790, "dur": 98, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Core.Editor.Shared.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1748533079149889, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748533079149993, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748533079150123, "dur": 53, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.InputSystem.DocCodeSamples.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1748533079150180, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748533079150299, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.AI.Navigation.Editor.dll"}}, {"pid": 12345, "tid": 5, "ts": 1748533079150371, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748533079150472, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/FMODUnity.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1748533079150555, "dur": 152, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748533079150749, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748533079150877, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748533079151008, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748533079151153, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748533079151312, "dur": 196, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748533079151508, "dur": 622483, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748533079774009, "dur": 123, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library/Bee/artifacts/200b0aE.dag/post-processed/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1748533079773993, "dur": 140, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1748533079774153, "dur": 845, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1748533079774999, "dur": 52711, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748533078828332, "dur": 66215, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748533078894547, "dur": 2682, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_B0CD21B816834329.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748533078897236, "dur": 2153, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_5637F04715021BE0.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748533078899405, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_119E2929175A73E5.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748533078899580, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_3BDCA2C56F5A3F82.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748533078899664, "dur": 468, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_FED4EADA5A496243.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748533078900152, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VideoModule.dll_150D0801C80CAAF8.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748533078900260, "dur": 482, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748533078900746, "dur": 111, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_4D1EEC228562B063.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748533078900857, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748533078900921, "dur": 151, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_242B47D8D2C44484.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748533078901073, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748533078901143, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_6F2F737294BA2646.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748533078901228, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748533078901298, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_1C9B2F49E4A7FD04.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748533078901390, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748533078901475, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_DB39C465F989DB9D.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748533078901611, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WebGL.Extensions.dll_50F42E90481F425E.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748533078901739, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.IonicZip.dll_C359BB03E2AB95AC.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748533078901898, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AMDModule.dll_7ADE8004EB70550A.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748533078902155, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748533078902207, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Pdb.dll_336F3065DC28AE59.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748533078902321, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748533078902512, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748533078902609, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748533078902699, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748533078902777, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748533078902839, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748533078903095, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748533078903228, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748533078903366, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748533078903460, "dur": 70, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1748533078903533, "dur": 163, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748533078903697, "dur": 57, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualStudio.Editor.rsp2"}}, {"pid": 12345, "tid": 6, "ts": 1748533078903755, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748533078903947, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748533078904003, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748533078904087, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748533078904184, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748533078904298, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748533078904435, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748533078904558, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748533078904665, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748533078904744, "dur": 161, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748533078904907, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748533078905011, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748533078905151, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748533078905271, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748533078905400, "dur": 68, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.PerformanceTesting.Editor.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1748533078905468, "dur": 1507, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748533078906975, "dur": 1087, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748533078908062, "dur": 1057, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748533078909119, "dur": 773, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748533078909893, "dur": 903, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748533078910796, "dur": 773, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748533078911570, "dur": 849, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748533078912419, "dur": 916, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748533078913336, "dur": 881, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748533078914217, "dur": 725, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748533078914942, "dur": 815, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748533078915757, "dur": 1038, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748533078916796, "dur": 1100, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748533078918860, "dur": 764, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.shadergraph@afc42d8f5d23/Editor/Data/Nodes/Input/Geometry/TangentVectorNode.cs"}}, {"pid": 12345, "tid": 6, "ts": 1748533078917896, "dur": 2658, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748533078920555, "dur": 1423, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748533078921978, "dur": 1352, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748533078923330, "dur": 1068, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748533078924398, "dur": 1163, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748533078925562, "dur": 1751, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748533078927313, "dur": 987, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748533078928300, "dur": 911, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748533078929211, "dur": 945, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748533078930156, "dur": 998, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748533078931155, "dur": 948, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748533078932103, "dur": 890, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748533078932993, "dur": 870, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748533078933863, "dur": 839, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748533078934702, "dur": 1630, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748533078936332, "dur": 1862, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748533078938194, "dur": 2301, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748533078940512, "dur": 2614, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748533078943127, "dur": 2564, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748533078945692, "dur": 2406, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748533078948102, "dur": 2649, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748533078950759, "dur": 1386, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748533078952145, "dur": 1242, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748533078953387, "dur": 1302, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748533078954689, "dur": 1079, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748533078955769, "dur": 1612, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748533078957381, "dur": 2085, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.collab-proxy@50ac96531b63/Editor/Views/Merge/Developer/DirectoryConflicts/DrawDirectoryResolutionPanel.cs"}}, {"pid": 12345, "tid": 6, "ts": 1748533078959678, "dur": 4759, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.collab-proxy@50ac96531b63/Editor/Views/Merge/Developer/DirectoryConflicts/DeleteChangeMenu.cs"}}, {"pid": 12345, "tid": 6, "ts": 1748533078964930, "dur": 2588, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.collab-proxy@50ac96531b63/Editor/Views/Merge/Developer/BuildMergeTabParameters.cs"}}, {"pid": 12345, "tid": 6, "ts": 1748533078957381, "dur": 10613, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748533078967994, "dur": 985, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748533078968979, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEditor.TestRunner.dll"}}, {"pid": 12345, "tid": 6, "ts": 1748533078969036, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748533078969142, "dur": 1205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748533078970347, "dur": 428, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748533078970805, "dur": 83, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748533078970915, "dur": 283, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748533078971198, "dur": 528, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748533078971730, "dur": 650, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Utilities.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748533078972420, "dur": 1043, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Utilities.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748533078973464, "dur": 187, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748533078973658, "dur": 123, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.ShaderGraph.Utilities.ref.dll_9587680413563429.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748533078973781, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748533078973876, "dur": 150, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748533078974027, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748533078974102, "dur": 1653, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748533078975755, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748533078975903, "dur": 237, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Mathematics.Editor.ref.dll_4A3A50F3774BA9F7.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748533078976141, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748533078976200, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748533078976336, "dur": 1031, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748533078977367, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748533078977456, "dur": 449, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Collections.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748533078977905, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748533078977991, "dur": 1122, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748533078979153, "dur": 2272, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748533078981425, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748533078981545, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748533078981613, "dur": 117, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748533078981730, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748533078981783, "dur": 1322, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748533078983106, "dur": 144, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748533078983311, "dur": 123, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748533078983434, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748533078983533, "dur": 1724, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748533078985258, "dur": 358, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748533078985633, "dur": 153, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748533078985793, "dur": 667, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Collections.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748533078986461, "dur": 204, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748533078986678, "dur": 928, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.AI.Navigation.Updater.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748533078987640, "dur": 231, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748533078987890, "dur": 3756, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748533078991646, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748533078991803, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Multiplayer.Center.Editor.ref.dll_6DAF32D8644B89D6.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748533078991923, "dur": 1012, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.DocCodeSamples.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748533078992935, "dur": 190, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748533078993134, "dur": 169, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748533078993307, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualStudio.Editor.dll"}}, {"pid": 12345, "tid": 6, "ts": 1748533078993373, "dur": 190, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748533078993575, "dur": 1649, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748533078995236, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748533078995312, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748533078995383, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748533078995480, "dur": 137, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748533078995626, "dur": 99, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Burst.CodeGen.pdb"}}, {"pid": 12345, "tid": 6, "ts": 1748533078995772, "dur": 1264, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748533078997038, "dur": 179, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748533078997235, "dur": 422, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748533078997657, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748533078997749, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748533078997803, "dur": 510, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748533078998314, "dur": 105793, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748533079104113, "dur": 3045, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748533079107160, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748533079107241, "dur": 3169, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Mathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748533079110457, "dur": 2043, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.PerformanceTesting.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748533079112502, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748533079112615, "dur": 2438, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748533079115054, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748533079115159, "dur": 8108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748533079123269, "dur": 253, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748533079123536, "dur": 7883, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Searcher.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748533079131419, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748533079131515, "dur": 8717, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Collections.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748533079140233, "dur": 201, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748533079140444, "dur": 11001, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.Shared.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748533079151516, "dur": 675522, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748533079827124, "dur": 498, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 7, "ts": 1748533078828324, "dur": 66183, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748533078894509, "dur": 2681, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreBusinessMetricsModule.dll_BA0B4FF91CE5F8A1.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748533078897217, "dur": 2549, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphicsStateCollectionSerializerModule.dll_C9A2ED22AC71DAA3.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748533078899865, "dur": 364, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TerrainModule.dll_7C523B7BB1DAFB72.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748533078900618, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748533078900731, "dur": 99, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_AB798D326E6716CC.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748533078900830, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748533078900907, "dur": 142, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_6ECCF42616E9E2B7.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748533078901049, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748533078901108, "dur": 113, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_7FABF25E82FCDDCD.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748533078901221, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748533078901320, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748533078901401, "dur": 127, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_66BE93EAC03D0429.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748533078901622, "dur": 102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.OSXStandalone.Extensions.dll_E38DB30ED3E699A5.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748533078901724, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748533078901798, "dur": 95, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.YamlDotNet.dll_4228628CD1B97490.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748533078901944, "dur": 3817, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748533078905762, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748533078905831, "dur": 13968, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748533078919800, "dur": 230, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748533078920046, "dur": 452, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UI.ref.dll_79746E5030301F94.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748533078920499, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748533078920618, "dur": 1375, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748533078921993, "dur": 1344, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748533078923337, "dur": 1058, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748533078924395, "dur": 1185, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748533078925580, "dur": 1775, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748533078927355, "dur": 958, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748533078928313, "dur": 904, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748533078929218, "dur": 944, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748533078930162, "dur": 983, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748533078931145, "dur": 965, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748533078932110, "dur": 893, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748533078933004, "dur": 866, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748533078933870, "dur": 853, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748533078934723, "dur": 1630, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748533078936353, "dur": 1827, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748533078938180, "dur": 2334, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748533078940514, "dur": 2698, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748533078943213, "dur": 2540, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748533078945754, "dur": 2321, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748533078948077, "dur": 2563, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748533078950641, "dur": 1455, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748533078952096, "dur": 1140, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748533078953237, "dur": 1416, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748533078954654, "dur": 1059, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748533078955714, "dur": 1237, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748533078956952, "dur": 2469, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.collab-proxy@50ac96531b63/Editor/Views/Merge/Gluon/IncomingChangesSelection.cs"}}, {"pid": 12345, "tid": 7, "ts": 1748533078959754, "dur": 4707, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.collab-proxy@50ac96531b63/Editor/Views/Merge/Developer/UnityMergeTree.cs"}}, {"pid": 12345, "tid": 7, "ts": 1748533078964684, "dur": 2753, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.collab-proxy@50ac96531b63/Editor/Views/Merge/Developer/MergeSelection.cs"}}, {"pid": 12345, "tid": 7, "ts": 1748533078956952, "dur": 11018, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748533078967970, "dur": 973, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748533078968965, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748533078969078, "dur": 1199, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748533078970278, "dur": 900, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748533078971178, "dur": 510, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748533078971691, "dur": 349, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748533078972041, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748533078972094, "dur": 1380, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748533078973475, "dur": 255, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748533078973846, "dur": 401, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Collections.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748533078974286, "dur": 1384, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Collections.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748533078975670, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748533078975786, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Collections.ref.dll_CB2B1FA2A63FF8D9.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748533078975841, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748533078975941, "dur": 681, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Collections.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748533078976664, "dur": 241, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748533078976905, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748533078977006, "dur": 3208, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748533078980215, "dur": 184, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748533078980438, "dur": 330, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748533078980768, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748533078980891, "dur": 16033, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748533078996996, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748533078997099, "dur": 1100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748533078998229, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748533078998294, "dur": 478, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748533078998830, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748533078998916, "dur": 490, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748533078999431, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748533078999516, "dur": 305, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748533078999824, "dur": 70, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748533078999911, "dur": 497884, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748533079498553, "dur": 374, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748533079499551, "dur": 162713, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748533079663010, "dur": 1403, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748533079664951, "dur": 211, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748533079823523, "dur": 383, "ph": "X", "name": "EmitNodeFinish", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748533079665511, "dur": 158405, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/200b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748533079827104, "dur": 505, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1748533079827612, "dur": 114, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748533078828292, "dur": 65687, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748533078894319, "dur": 1229, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "/Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/Tools/BuildPipeline"}}, {"pid": 12345, "tid": 8, "ts": 1748533078895548, "dur": 5662, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "/Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/Tools/Compilation/Unity.ILPP.Runner"}}, {"pid": 12345, "tid": 8, "ts": 1748533078901210, "dur": 658, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "/Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/Tools/Compilation/Unity.ILPP.Trigger"}}, {"pid": 12345, "tid": 8, "ts": 1748533078893981, "dur": 7888, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748533078901981, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748533078902186, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.CompilationPipeline.Common.dll_18DD15F206BB0EF4.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748533078902300, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748533078902425, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748533078902590, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748533078902729, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748533078902806, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748533078903069, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748533078903146, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748533078903279, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748533078903402, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748533078903547, "dur": 179, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748533078903754, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748533078903867, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748533078904024, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748533078904100, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748533078904216, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748533078904334, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748533078904454, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748533078904531, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748533078904655, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748533078904735, "dur": 138, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748533078904876, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748533078904995, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748533078905133, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748533078905231, "dur": 146, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748533078905377, "dur": 58, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.CollabProxy.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 8, "ts": 1748533078905436, "dur": 1737, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748533078907174, "dur": 1027, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748533078908201, "dur": 1104, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748533078909305, "dur": 813, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748533078910118, "dur": 842, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748533078910960, "dur": 734, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748533078911694, "dur": 884, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748533078912578, "dur": 952, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748533078913530, "dur": 810, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748533078914340, "dur": 794, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748533078915134, "dur": 888, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748533078916022, "dur": 988, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748533078917010, "dur": 1459, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748533078918855, "dur": 762, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.shadergraph@afc42d8f5d23/Editor/Data/Legacy/SerializableGuid.cs"}}, {"pid": 12345, "tid": 8, "ts": 1748533078918470, "dur": 2392, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748533078920862, "dur": 1472, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748533078922334, "dur": 1365, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748533078923699, "dur": 957, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748533078924656, "dur": 1247, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748533078925903, "dur": 1775, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748533078927678, "dur": 920, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748533078928599, "dur": 850, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748533078929449, "dur": 887, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748533078930336, "dur": 964, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748533078931300, "dur": 997, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748533078932297, "dur": 856, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748533078933154, "dur": 850, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748533078934004, "dur": 845, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748533078934849, "dur": 1752, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748533078936601, "dur": 2059, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748533078938660, "dur": 2097, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748533078940757, "dur": 3008, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748533078943765, "dur": 2231, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748533078945997, "dur": 2487, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748533078948486, "dur": 2404, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748533078950890, "dur": 1341, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748533078952231, "dur": 1275, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748533078953506, "dur": 1323, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748533078954829, "dur": 1080, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748533078956998, "dur": 2448, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.mathematics@8017b507cc74/Unity.Mathematics/float2.gen.cs"}}, {"pid": 12345, "tid": 8, "ts": 1748533078955910, "dur": 3723, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748533078959761, "dur": 4702, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.collab-proxy@50ac96531b63/Editor/Views/History/HistoryListViewMenu.cs"}}, {"pid": 12345, "tid": 8, "ts": 1748533078964686, "dur": 2753, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.collab-proxy@50ac96531b63/Editor/Views/DownloadPlasticExeDialog.cs"}}, {"pid": 12345, "tid": 8, "ts": 1748533078959634, "dur": 8477, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748533078968111, "dur": 1122, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748533078969234, "dur": 1141, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748533078970375, "dur": 369, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748533078970916, "dur": 337, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748533078971253, "dur": 475, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748533078971731, "dur": 89, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748533078971859, "dur": 3403, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748533078975262, "dur": 168, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748533078975488, "dur": 709, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748533078976228, "dur": 5023, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748533078981252, "dur": 149, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748533078981404, "dur": 2389, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748533078983794, "dur": 150, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748533078983947, "dur": 4075, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/FMODUnity.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748533078988023, "dur": 165, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748533078988196, "dur": 656, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/FMODUnity.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748533078988852, "dur": 201, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748533078989069, "dur": 1087, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/FMODUnityResonance.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748533078990157, "dur": 227, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748533078990397, "dur": 5896, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/FMODUnityResonanceEditor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748533078996293, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748533078996429, "dur": 1870, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748533078998300, "dur": 192, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/FMODUnityEditor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748533078998492, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748533078998557, "dur": 464, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/FMODUnityEditor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748533078999072, "dur": 105136, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748533079104209, "dur": 5346, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748533079109556, "dur": 154, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748533079109721, "dur": 4553, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Rendering.LightTransport.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748533079114275, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748533079114391, "dur": 4490, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/FMODUnityResonance.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748533079118882, "dur": 1397, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748533079120304, "dur": 5688, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748533079125993, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748533079126127, "dur": 4937, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Rendering.LightTransport.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748533079131065, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748533079131188, "dur": 7741, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748533079138930, "dur": 230, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748533079139169, "dur": 3464, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.PerformanceTesting.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748533079142633, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748533079142736, "dur": 5254, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/FMODUnityEditor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748533079147991, "dur": 135, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748533079148132, "dur": 127, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/FMODUnityEditor.pdb"}}, {"pid": 12345, "tid": 8, "ts": 1748533079148260, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748533079148393, "dur": 114, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.PerformanceTesting.Editor.dll"}}, {"pid": 12345, "tid": 8, "ts": 1748533079148507, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748533079148648, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748533079148798, "dur": 230, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748533079149034, "dur": 153, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748533079149196, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.SettingsProvider.Editor.pdb"}}, {"pid": 12345, "tid": 8, "ts": 1748533079149282, "dur": 177, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748533079149467, "dur": 157, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748533079149633, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Searcher.Editor.dll"}}, {"pid": 12345, "tid": 8, "ts": 1748533079149697, "dur": 180, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748533079149920, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/FMODUnityResonanceEditor.pdb"}}, {"pid": 12345, "tid": 8, "ts": 1748533079149979, "dur": 171, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748533079150150, "dur": 93, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/FMODUnityResonanceEditor.pdb"}}, {"pid": 12345, "tid": 8, "ts": 1748533079150248, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748533079150403, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748533079150534, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748533079150617, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Universal.Runtime.pdb"}}, {"pid": 12345, "tid": 8, "ts": 1748533079150668, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748533079150796, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748533079150916, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748533079151046, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748533079151241, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748533079151341, "dur": 906, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748533079152294, "dur": 675355, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748533079830680, "dur": 588, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 27554, "tid": 6381, "ts": 1748533079837616, "dur": 26, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 27554, "tid": 6381, "ts": 1748533079837694, "dur": 909, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 27554, "tid": 6381, "ts": 1748533079835480, "dur": 3166, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}