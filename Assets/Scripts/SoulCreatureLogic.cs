using UnityEngine;
using System.Collections.Generic;
using System;
using FMODUnity;
using Random = UnityEngine.Random;

public interface ISoulCreatureBoostProvider
{
    float GetFlightBoostValue();
}

public class SoulCreatureLogic : MonoBehaviour, ISoulCreatureBoostProvider
{
    // Static registry of all active SoulCreatureLogic instances
    private static readonly List<SoulCreatureLogic> ActiveInstances = new List<SoulCreatureLogic>();

    // Static method to get all active instances
    public static List<SoulCreatureLogic> GetAllActiveInstances()
    {
        return ActiveInstances;
    }

    #region Core Components and References

    // Core references
    private Transform playerTransform;
    private PlayerController playerController;
    private ParticleSystem ps;
    private CreatureMovement creatureMovement;
    private Camera mainCamera;

    // Boundary values for movement constraints
    [SerializeField] public float minY = 20;
    [SerializeField] public float maxY = 50;

    // Public access to average particle position
    [HideInInspector] public Vector3 averageParticlePosition;

    [Header("Particle System Settings")]
    [Tooltip("Minimum number of max particles")]
    [SerializeField] private int minMaxParticles = 200;

    [Tooltip("Maximum number of max particles")]
    [SerializeField] private int maxMaxParticles = 1000;

    // Color settings for ocean creatures
    [Header("Ocean Creature Color Settings")]
    [Tooltip("Whether this is an ocean-type creature that should have random colors")]
    public bool isOceanCreature = false;

    [Tooltip("Minimum color hue (0-1)")]
    [Range(0f, 1f)] public float minHue = 0.5f;

    [Tooltip("Maximum color hue (0-1)")]
    [Range(0f, 1f)] public float maxHue = 0.7f;

    [Tooltip("Saturation for the color (0-1)")]
    [Range(0f, 1f)] public float colorSaturation = 0.8f;

    [Tooltip("Brightness for the color (0-1)")]
    [Range(0f, 1f)] public float colorBrightness = 0.8f;

    // Render queue settings for ocean creatures
    [Header("Render Queue Settings")]
    [Tooltip("Whether to use the centralized render queue system in GameManager")]
    public bool useCentralizedRenderQueue = true;

    #endregion

    #region Module Toggles

    [Header("Module Toggles")]
    [Tooltip("Enable/disable particle following behavior")]
    public bool enableParticleFollowing = true;

    #endregion

    #region Particle Following Settings

    [Header("Particle Following Settings")]
    [Tooltip("Minimum value for minimum speed particles can move at")]
    [SerializeField] private float minMinSpeed = 1f;

    [Tooltip("Maximum value for minimum speed particles can move at")]
    [SerializeField] private float maxMinSpeed = 5f;

    [Tooltip("Current randomized minimum speed")]
    [SerializeField] private float minSpeed = 1f;

    [Tooltip("Minimum value for maximum speed particles can move at")]
    [SerializeField] private float minMaxSpeed = 10f;

    [Tooltip("Maximum value for maximum speed particles can move at")]
    [SerializeField] private float maxMaxSpeed = 20f;

    [Tooltip("Current randomized maximum speed")]
    [SerializeField] private float maxSpeed = 10f;

    [Tooltip("Rate at which particle speed interpolates to desired speed")]
    [SerializeField] private float lerpFactor = 2f;

    [Tooltip("Distance from target where particles start following")]
    [SerializeField] private float followThreshold = 1f;

    [Tooltip("Distance from target where particles stop following")]
    [SerializeField] private float stopThreshold = 0.3f;

    [Tooltip("Minimum value for maximum distance at which speed scaling reaches its maximum effect")]
    [SerializeField] private float minMaxDistance = 3f;

    [Tooltip("Maximum value for maximum distance at which speed scaling reaches its maximum effect")]
    [SerializeField] private float maxMaxDistance = 20f;

    [Tooltip("Current randomized maximum distance")]
    [SerializeField] private float maxDistance = 5f;

    [Header("Trail Following Settings")]
    [Tooltip("Enable trail-following mode instead of direct position following")]
    [SerializeField] private bool enableTrailFollowing = false;

    [Tooltip("Maximum number of trail positions to record")]
    [SerializeField] private int maxTrailLength = 100;

    [Tooltip("How often to record trail positions (in seconds)")]
    [SerializeField] private float trailRecordInterval = 0.05f;

    [Tooltip("Length of trail that particles can follow (in world units)")]
    [SerializeField] private float trailFollowDistance = 20f;

    [Tooltip("How smoothly particles interpolate between trail positions")]
    [SerializeField] private float trailInterpolationFactor = 5f;

    [Header("Trail Debug Settings")]
    [Tooltip("Show debug gizmos for trail following system")]
    [SerializeField] private bool showTrailDebug = false;

    [Tooltip("Show trail points as spheres")]
    [SerializeField] private bool showTrailPoints = true;

    [Tooltip("Show lines connecting trail points")]
    [SerializeField] private bool showTrailLines = true;

    [Tooltip("Show particle target positions")]
    [SerializeField] private bool showParticleTargets = true;

    [Tooltip("Size of debug spheres")]
    [SerializeField] private float debugSphereSize = 0.2f;

    [Tooltip("Print debug messages to console")]
    [SerializeField] private bool enableConsoleDebug = false;

    [Header("Particle Update Rate")]
    [Tooltip("How often (in seconds) to update particle movement. Lower is smoother, higher is more performant.")]
    [SerializeField] private float particleUpdateInterval = 0.01f;
    [Tooltip("Maximum deltaTime to prevent large jumps during FPS drops")]
    [SerializeField] private float maxDeltaTime = 0.05f;
    private float particleUpdateTimer = 0f;

    [Header("Distance-Based Performance Settings")]
    [Tooltip("Distance from player where performance optimization starts")]
    [SerializeField] private float performanceOptimizationStartDistance = 10f;
    [Tooltip("Distance from player where maximum performance optimization is applied")]
    [SerializeField] private float performanceOptimizationMaxDistance = 30f;
    [Tooltip("Particle update interval for creatures close to player")]
    [SerializeField] private float minDistanceParticleUpdateInterval = 0.01f;
    [Tooltip("Particle update interval for creatures at maximum distance")]
    [SerializeField] private float maxDistanceParticleUpdateInterval = 0.1f;

    // Performance optimization variables
    private float currentParticleUpdateInterval;

    // Render queue management
    private bool materialsRegistered = false;

    // Particle following internal variables
    private Mesh shapeMesh;
    private ParticleSystem.Particle[] particlesArray;
    private ParticleData[] particlesData;
    private float[] triangleAreas;
    private float totalArea;

    // Trail following variables
    private TrailPoint[] trailPoints;
    private int trailStartIndex = 0;
    private int trailCount = 0;
    private float trailRecordTimer = 0f;
    private Vector3 lastRecordedPosition;

    // Debug variables
    private List<Vector3> debugParticleTargets = new List<Vector3>();

    private struct ParticleData
    {
        public Vector3 TargetLocalPos;
        public float DesiredSpeed;
        public float CurrentSpeed;
        public float Timer;
        public bool IsFollowing;
        public bool initialized;
        public Vector3 Velocity;
        public float TrailDistance; // Distance along trail for trail-following mode
    }

    private struct TrailPoint
    {
        public Vector3 Position;
        public Quaternion Rotation;
        public float Timestamp;
        public float DistanceFromStart;
    }

    #endregion

    [Header("Flight Boost")]
    [Tooltip("Amount of flight boost this soul creature provides to the player")]
    public float flightBoostValue = 6f;

    #region Audio and Gravity Control Settings

    // Audio component reference

    [Header("Gravity Control Settings")]
    [Tooltip("Number of particle collisions required to disable player gravity")]
    [SerializeField] private int particleCollisionsToDisableGravity = 10;

    [Tooltip("Time in seconds after which gravity is re-enabled if no particle collisions occur")]
    [SerializeField] private float gravityReenableDelay = 1.0f;

    [Tooltip("Enable debug logging for gravity control")]
    [SerializeField] private bool debugGravityControl = false;

    // Gravity control variables
    private int currentParticleCollisionCount = 0;
    private float lastParticleCollisionTime = -Mathf.Infinity;
    private bool isGravityDisabled = false;

    #endregion

    #region Unity Lifecycle Methods

    private void Awake()
    {
        if (enableParticleFollowing)
        {
            InitializeParticleSystem();
        }
    }

    private void OnEnable()
    {
        // Register this instance in the static list
        if (!ActiveInstances.Contains(this))
        {
            ActiveInstances.Add(this);
        }
    }

    private void OnDisable()
    {
        // Remove this instance from the static list
        ActiveInstances.Remove(this);

        // Clean up gravity control
        CleanupGravityControl();
    }

    private void Start()
    {
        // Get references from GameManager
        if (GameManager.Instance != null)
        {
            playerTransform = GameManager.Instance.player.transform;
            playerController = GameManager.Instance.player;
        }
        else
        {
            //            Debug.LogError("GameManager instance not found!");
            enabled = false;
            return;
        }

        // Get CreatureMovement component
        creatureMovement = GetComponent<CreatureMovement>();
        if (creatureMovement == null)
        {
            Debug.LogWarning($"CreatureMovement component not found on {gameObject.name}. Movement functionality will be disabled.");
        }

        // Get main camera reference
        mainCamera = Camera.main;
        if (mainCamera == null)
        {
            Debug.LogWarning("Main camera not found!");
        }

        // Debug log to verify player tag
        GameObject player = GameObject.FindGameObjectWithTag("Player");
        if (player != null)
        {
            //            Debug.Log($"Found player with tag 'Player': {player.name}");
        }
        else
        {
            Debug.LogWarning("No GameObject with tag 'Player' found in the scene!");
        }

        // Make sure the player from GameManager has the Player tag
        if (playerTransform != null && !playerTransform.CompareTag("Player"))
        {
            Debug.LogWarning($"Player GameObject from GameManager doesn't have the 'Player' tag! Current tag: {playerTransform.tag}");
            playerTransform.tag = "Player";
            Debug.Log("Set player tag to 'Player'");
        }

        // Initialize particle system
        ps = GetComponent<ParticleSystem>();
        if (ps == null && enableParticleFollowing)
        {
            Debug.LogError("No ParticleSystem found on this GameObject!");
            enabled = false;
            return;
        }

        // Randomize particle system max particles
        var main = ps.main;
        int randomMaxParticles = Random.Range(minMaxParticles, maxMaxParticles + 1);
        var mainModule = main;
        mainModule.maxParticles = randomMaxParticles;

        // Randomize particle following settings
        minSpeed = Random.Range(minMinSpeed, maxMinSpeed);
        maxSpeed = Random.Range(minMaxSpeed, maxMaxSpeed);
        maxDistance = Random.Range(minMaxDistance, maxMaxDistance);

        // Initialize particle following
        if (enableParticleFollowing)
        {
            particlesArray = new ParticleSystem.Particle[ps.main.maxParticles];
            PrecomputeTriangleAreas();

            // Initialize trail following system
            if (enableTrailFollowing)
            {
                InitializeTrailSystem();
            }
        }

        // Apply random color for ocean creatures
        if (isOceanCreature && ps != null)
        {
            ApplyRandomColor();
        }

        // Register materials with render queue system once (replaces expensive per-frame updates)
        RegisterMaterialsWithRenderQueueSystem();
    }

    private void Update()
    {
        if (playerTransform == null || playerController == null) return;

        // Calculate distance to player for performance optimization
        float distanceToPlayer = Vector3.Distance(transform.position, playerTransform.position);
        UpdatePerformanceSettings(distanceToPlayer);

        // Update trail recording if trail following is enabled
        if (enableParticleFollowing && enableTrailFollowing)
        {
            UpdateTrailRecording();
        }

        // Update particle following with distance-based interval
        if (enableParticleFollowing && ps != null && shapeMesh != null)
        {
            averageParticlePosition = GetAverageParticlePosition();
            UpdateParticles();
        }

        // Check if we need to re-enable gravity
        CheckGravityReenabling();
    }

    private void CheckGravityReenabling()
    {
        // Only check if gravity is currently disabled by this soul creature
        if (!isGravityDisabled) return;

        // Check if enough time has passed since the last particle collision
        if (Time.time - lastParticleCollisionTime >= gravityReenableDelay)
        {
            // Reset collision count
            currentParticleCollisionCount = 0;

            // Re-enable gravity if the player controller exists
            if (playerController != null)
            {
                // We need to check if any other SoulCreatureLogic is currently disabling gravity
                bool otherCreatureDisablingGravity = false;

                // Check all other active SoulCreatureLogic objects using our static list
                foreach (SoulCreatureLogic creature in ActiveInstances)
                {
                    // Skip this creature
                    if (creature == this) continue;

                    // Check if this other creature is disabling gravity
                    if (creature.isGravityDisabled)
                    {
                        otherCreatureDisablingGravity = true;
                        break;
                    }
                }

                // Only re-enable gravity if no other creature is disabling it
                if (!otherCreatureDisablingGravity)
                {
                    playerController.SetGravityDisabled(false);

                    if (debugGravityControl)
                    {
                        Debug.Log($"Re-enabled gravity for player after no interactions with {gameObject.name} for {gravityReenableDelay} seconds");
                    }
                }
                else if (debugGravityControl)
                {
                    Debug.Log($"{gameObject.name} would re-enable gravity, but another soul creature is still disabling it");
                }

                // Mark this creature as no longer disabling gravity
                isGravityDisabled = false;
            }
        }
    }

    private void UpdatePerformanceSettings(float distanceToPlayer)
    {
        // Calculate performance optimization factor based on distance
        float optimizationFactor = 0f;
        if (distanceToPlayer > performanceOptimizationStartDistance)
        {
            optimizationFactor = Mathf.InverseLerp(
                performanceOptimizationStartDistance,
                performanceOptimizationMaxDistance,
                distanceToPlayer
            );
        }

        // Update particle update interval based on distance
        currentParticleUpdateInterval = Mathf.Lerp(
            minDistanceParticleUpdateInterval,
            maxDistanceParticleUpdateInterval,
            optimizationFactor
        );

        // No longer needed - render queue registration is now one-time only
    }

    /// <summary>
    /// Register materials with the centralized render queue system once during initialization.
    /// This replaces the expensive per-frame UpdateRenderQueue calls.
    /// </summary>
    private void RegisterMaterialsWithRenderQueueSystem()
    {
        if (materialsRegistered || !isOceanCreature || !useCentralizedRenderQueue || GameManager.Instance == null)
            return;

        // Get all renderers (main and child particle systems) - only once!
        ParticleSystemRenderer[] renderers = GetComponentsInChildren<ParticleSystemRenderer>();

        // Register all materials with the centralized system
        foreach (ParticleSystemRenderer renderer in renderers)
        {
            foreach (Material material in renderer.materials)
            {
                if (material != null)
                {
                    GameManager.Instance.AddDynamicRenderQueueMaterial(material);
                }
            }
        }

        materialsRegistered = true;
    }

    private void OnParticleCollision(GameObject other)
    {
        if (other.CompareTag("Player"))
        {
            // Handle gravity control
            HandleGravityControl();
        }
    }

    private void HandleGravityControl()
    {
        // Update collision count and time
        currentParticleCollisionCount++;
        lastParticleCollisionTime = Time.time;

        // Check if we've reached the threshold to disable gravity
        if (!isGravityDisabled && currentParticleCollisionCount >= particleCollisionsToDisableGravity)
        {
            if (playerController != null)
            {
                playerController.SetGravityDisabled(true);
                isGravityDisabled = true;

                if (debugGravityControl)
                {
                    Debug.Log($"Disabled gravity for player due to {gameObject.name} particle interactions");
                }
            }
        }
    }

    private void OnDestroy()
    {
        // Clean up any active audio events
        StopAllCoroutines();

        // Clean up materials from the centralized render queue system
        if (materialsRegistered && GameManager.Instance != null)
        {
            ParticleSystemRenderer[] renderers = GetComponentsInChildren<ParticleSystemRenderer>();
            foreach (ParticleSystemRenderer renderer in renderers)
            {
                foreach (Material material in renderer.materials)
                {
                    if (material != null)
                    {
                        GameManager.Instance.RemoveDynamicRenderQueueMaterial(material);
                    }
                }
            }
            materialsRegistered = false;
        }

        // Clean up gravity control
        CleanupGravityControl();
    }

    void CleanupGravityControl()
    {
        // Only do cleanup if this soul creature is currently disabling gravity
        if (isGravityDisabled && playerController != null)
        {
            // We need to check if any other SoulCreatureLogic is currently disabling gravity
            bool otherCreatureDisablingGravity = false;

            // Check all other active SoulCreatureLogic objects using our static list
            foreach (SoulCreatureLogic creature in ActiveInstances)
            {
                // Skip this creature
                if (creature == this) continue;

                // Check if this other creature is disabling gravity
                if (creature.isGravityDisabled)
                {
                    otherCreatureDisablingGravity = true;
                    break;
                }
            }

            // Only re-enable gravity if no other creature is disabling it
            if (!otherCreatureDisablingGravity)
            {
                playerController.SetGravityDisabled(false);

                if (debugGravityControl)
                {
                    Debug.Log($"Re-enabled gravity for player due to {gameObject.name} being disabled or destroyed");
                }
            }

            // Reset state
            isGravityDisabled = false;
            currentParticleCollisionCount = 0;
        }
    }

    #region ISoulCreatureBoostProvider Implementation

    float ISoulCreatureBoostProvider.GetFlightBoostValue() => flightBoostValue;

    #endregion

    #region Color Randomization

    void ApplyRandomColor()
    {
        var audio = GetComponent<SoulCreatureAudio>();
        if (audio != null)
        {
            int soundPairCount = audio.soundPairs != null ? audio.soundPairs.Count : 0;
            int soundPairIndex = audio.SelectedSoundPairIndex;
            Color color = SoulCreatureColorManager.GetColorForSoundPair(soundPairCount, soundPairIndex);
            // Apply the color to the particle system or renderer as needed
            if (ps != null)
            {
                var main = ps.main;
                main.startColor = color;
            }
        }
    }

    #endregion

    #region Particle Following Implementation

    private void InitializeParticleSystem()
    {
        ps = GetComponent<ParticleSystem>();
        if (ps.shape.shapeType != ParticleSystemShapeType.Mesh || (shapeMesh = ps.shape.mesh) == null)
        {
            enableParticleFollowing = false;
            return;
        }
        int maxParticles = ps.main.maxParticles;
        particlesArray = new ParticleSystem.Particle[maxParticles];
        particlesData = new ParticleData[maxParticles];
    }

    private void PrecomputeTriangleAreas()
    {
        if (shapeMesh == null) return;

        int[] tris = shapeMesh.triangles;
        Vector3[] verts = shapeMesh.vertices;
        int triangleCount = tris.Length / 3;
        triangleAreas = new float[triangleCount];
        totalArea = 0f;

        for (int i = 0; i < triangleCount; i++)
        {
            Vector3 v0 = verts[tris[i * 3]];
            Vector3 v1 = verts[tris[i * 3 + 1]];
            Vector3 v2 = verts[tris[i * 3 + 2]];
            float area = 0.5f * Vector3.Cross(v1 - v0, v2 - v0).magnitude;
            triangleAreas[i] = area;
            totalArea += area;
        }
    }

    private void UpdateParticles()
    {
        particleUpdateTimer += Time.deltaTime;
        if (particleUpdateTimer < currentParticleUpdateInterval)
            return;
        float step = particleUpdateTimer;
        particleUpdateTimer = 0f;

        int count = ps.GetParticles(particlesArray);
        if (particlesData == null || particlesData.Length != particlesArray.Length)
        {
            particlesData = new ParticleData[particlesArray.Length];
        }

        // Clear debug targets for this frame
        if (showTrailDebug && showParticleTargets)
        {
            debugParticleTargets.Clear();
        }
        for (int i = 0; i < count; i++)
        {
            ref ParticleSystem.Particle particle = ref particlesArray[i];
            ref ParticleData data = ref particlesData[i];
            if (!data.initialized)
            {
                data = InitializeParticleData();
                data.initialized = true;
                data.Velocity = Vector3.zero;
            }
            UpdateParticleSpeed(ref data, step);

            Vector3 targetWorldPos;
            if (enableTrailFollowing && trailCount > 0)
            {
                targetWorldPos = GetTrailTargetPosition(ref data);
            }
            else
            {
                targetWorldPos = transform.TransformPoint(data.TargetLocalPos);
            }

            // Collect debug information
            if (showTrailDebug && showParticleTargets && i < 10) // Limit to first 10 particles for performance
            {
                debugParticleTargets.Add(targetWorldPos);
            }

            float distance = Vector3.Distance(particle.position, targetWorldPos);
            data.IsFollowing = data.IsFollowing ? distance >= stopThreshold : distance > followThreshold;
            if (data.IsFollowing)
            {
                Vector3 toTarget = (targetWorldPos - particle.position);
                float dist = toTarget.magnitude;
                Vector3 desiredVelocity = Vector3.zero;
                if (dist > 0.001f)
                {
                    float speed = data.CurrentSpeed * Mathf.Min(dist / maxDistance, 1f);
                    if (dist >= maxDistance)
                    {
                        float speedScaleFactor = Mathf.Max(dist / maxDistance, 1);
                        speed = (data.CurrentSpeed + 10) * speedScaleFactor;
                    }
                    desiredVelocity = toTarget.normalized * speed;
                }
                data.Velocity = Vector3.Lerp(data.Velocity, desiredVelocity, lerpFactor * step);
                if (dist < stopThreshold * 2f)
                {
                    data.Velocity *= Mathf.Lerp(0.1f, 1f, dist / (stopThreshold * 2f));
                }
                particle.position += data.Velocity * step;
            }
            else
            {
                data.Velocity = Vector3.Lerp(data.Velocity, Vector3.zero, lerpFactor * step);
            }
        }
        ps.SetParticles(particlesArray, count);
    }

    private ParticleData InitializeParticleData()
    {
        Vector3 localPos = GetRandomMeshPoint();
        float speed = Random.Range(minSpeed, maxSpeed);
        float trailDistance = enableTrailFollowing ? Random.Range(0f, trailFollowDistance) : 0f;

        return new ParticleData
        {
            TargetLocalPos = localPos,
            DesiredSpeed = speed,
            CurrentSpeed = speed,
            Timer = Random.Range(0f, 4f),
            IsFollowing = false,
            initialized = true,
            Velocity = Vector3.zero,
            TrailDistance = trailDistance
        };
    }

    private void UpdateParticleSpeed(ref ParticleData data, float deltaTime)
    {
        data.Timer += deltaTime;
        if (data.Timer > 4f)
        {
            data.Timer -= 4f;
            data.DesiredSpeed = Random.Range(minSpeed, maxSpeed);
        }
        data.CurrentSpeed = Mathf.Lerp(data.CurrentSpeed, data.DesiredSpeed, lerpFactor * deltaTime);
    }

    private Vector3 GetRandomMeshPoint()
    {
        if (shapeMesh == null) return Vector3.zero;

        int[] tris = shapeMesh.triangles;
        Vector3[] verts = shapeMesh.vertices;

        float randomValue = Random.value * totalArea;
        int triIndex = 0;
        float cumulativeArea = 0f;

        for (int i = 0; i < triangleAreas.Length; i++)
        {
            cumulativeArea += triangleAreas[i];
            if (randomValue <= cumulativeArea)
            {
                triIndex = i * 3;
                break;
            }
        }

        Vector3 v0 = verts[tris[triIndex]];
        Vector3 v1 = verts[tris[triIndex + 1]];
        Vector3 v2 = verts[tris[triIndex + 2]];

        float u = Random.value, v = Random.value;
        if (u + v > 1) { u = 1 - u; v = 1 - v; }

        return v0 + u * (v1 - v0) + v * (v2 - v0);
    }

    #endregion
    #region Trail Following Implementation

    private void InitializeTrailSystem()
    {
        trailPoints = new TrailPoint[maxTrailLength];
        trailStartIndex = 0;
        trailCount = 0;
        trailRecordTimer = 0f;
        lastRecordedPosition = transform.position;

        // Record initial position
        RecordTrailPoint();

        if (enableConsoleDebug)
        {
            Debug.Log($"[{gameObject.name}] Trail system initialized. Max length: {maxTrailLength}, Record interval: {trailRecordInterval}s");
        }
    }

    private void UpdateTrailRecording()
    {
        trailRecordTimer += Time.deltaTime;

        if (trailRecordTimer >= trailRecordInterval)
        {
            // Check if we've moved enough to warrant a new trail point
            float distanceMoved = Vector3.Distance(transform.position, lastRecordedPosition);
            if (distanceMoved > 0.1f) // Minimum distance threshold
            {
                RecordTrailPoint();
                trailRecordTimer = 0f;

                if (enableConsoleDebug && trailCount % 10 == 0) // Log every 10th point to avoid spam
                {
                    Debug.Log($"[{gameObject.name}] Recorded trail point {trailCount}. Distance moved: {distanceMoved:F2}");
                }
            }
        }
    }

    private void RecordTrailPoint()
    {
        int index = (trailStartIndex + trailCount) % maxTrailLength;

        float distanceFromStart = 0f;
        if (trailCount > 0)
        {
            int prevIndex = trailCount == 1 ? trailStartIndex : (index - 1 + maxTrailLength) % maxTrailLength;
            distanceFromStart = trailPoints[prevIndex].DistanceFromStart +
                               Vector3.Distance(transform.position, trailPoints[prevIndex].Position);
        }

        trailPoints[index] = new TrailPoint
        {
            Position = transform.position,
            Rotation = transform.rotation,
            Timestamp = Time.time,
            DistanceFromStart = distanceFromStart
        };

        if (trailCount < maxTrailLength)
        {
            trailCount++;
        }
        else
        {
            // Circular buffer - move start index
            trailStartIndex = (trailStartIndex + 1) % maxTrailLength;
        }

        lastRecordedPosition = transform.position;
    }

    private Vector3 GetTrailTargetPosition(ref ParticleData data)
    {
        if (trailCount == 0)
        {
            return transform.TransformPoint(data.TargetLocalPos);
        }

        // Find the position along the trail based on the particle's trail distance
        float targetDistance = data.TrailDistance;

        // Get the most recent trail point (current head position)
        int headIndex = trailCount == maxTrailLength ?
            (trailStartIndex + trailCount - 1) % maxTrailLength :
            (trailStartIndex + trailCount - 1);

        float totalTrailDistance = trailPoints[headIndex].DistanceFromStart;

        // Get current mesh scale for applying to trail positions
        Vector3 meshScale = GetMeshScale();
        Vector3 scaledLocalPos = Vector3.Scale(data.TargetLocalPos, meshScale);

        // If the target distance is beyond our trail, use the oldest point
        if (targetDistance >= totalTrailDistance)
        {
            Vector3 oldestPos = trailPoints[trailStartIndex].Position;
            return oldestPos + trailPoints[trailStartIndex].Rotation * scaledLocalPos;
        }

        // Find the trail segment that contains our target distance
        float searchDistance = totalTrailDistance - targetDistance;

        for (int i = 0; i < trailCount - 1; i++)
        {
            int currentIndex = trailCount == maxTrailLength ?
                (trailStartIndex + trailCount - 1 - i) % maxTrailLength :
                (trailStartIndex + trailCount - 1 - i);
            int nextIndex = trailCount == maxTrailLength ?
                (currentIndex - 1 + maxTrailLength) % maxTrailLength :
                (currentIndex - 1);

            if (nextIndex < 0) break;

            float currentDist = trailPoints[currentIndex].DistanceFromStart;
            float nextDist = trailPoints[nextIndex].DistanceFromStart;

            if (searchDistance >= nextDist && searchDistance <= currentDist)
            {
                // Interpolate between these two points
                float t = (searchDistance - nextDist) / (currentDist - nextDist);
                Vector3 interpolatedPos = Vector3.Lerp(trailPoints[nextIndex].Position, trailPoints[currentIndex].Position, t);
                Quaternion interpolatedRot = Quaternion.Lerp(trailPoints[nextIndex].Rotation, trailPoints[currentIndex].Rotation, t);

                // Apply the scaled local offset relative to the interpolated rotation
                return interpolatedPos + interpolatedRot * scaledLocalPos;
            }
        }

        // Fallback to current position with scaling
        return transform.TransformPoint(scaledLocalPos);
    }

    private Vector3 GetMeshScale()
    {
        // Get the current mesh scale from the particle system shape module
        if (ps != null && ps.shape.enabled)
        {
            return ps.shape.scale;
        }

        // Fallback to transform scale if particle system shape is not available
        return transform.localScale;
    }

    #endregion





    private Vector3 GetAverageParticlePosition()
    {
        if (ps == null) return transform.position;
        int count = ps.particleCount;
        if (count == 0) return transform.position;
        if (particlesArray == null || particlesArray.Length < count)
            particlesArray = new ParticleSystem.Particle[ps.main.maxParticles];
        int actualCount = ps.GetParticles(particlesArray);
        Vector3 sum = Vector3.zero;
        for (int i = 0; i < actualCount; i++)
            sum += particlesArray[i].position;
        return sum / Mathf.Max(1, actualCount);
    }

    #endregion

    #region Debug Visualization

#if UNITY_EDITOR
    private void OnDrawGizmos()
    {
        if (!showTrailDebug) return;

        // Draw trail points and connections
        if (enableTrailFollowing && trailPoints != null && trailCount > 0)
        {
            // Draw trail points
            if (showTrailPoints)
            {
                Gizmos.color = Color.yellow;
                for (int i = 0; i < trailCount; i++)
                {
                    int index = (trailStartIndex + i) % maxTrailLength;
                    Gizmos.DrawSphere(trailPoints[index].Position, debugSphereSize);
                }

                // Highlight the most recent point (head)
                if (trailCount > 0)
                {
                    int headIndex = trailCount == maxTrailLength ?
                        (trailStartIndex + trailCount - 1) % maxTrailLength :
                        (trailStartIndex + trailCount - 1);
                    Gizmos.color = Color.red;
                    Gizmos.DrawSphere(trailPoints[headIndex].Position, debugSphereSize * 1.5f);
                }
            }

            // Draw trail connections
            if (showTrailLines && trailCount > 1)
            {
                Gizmos.color = Color.cyan;
                for (int i = 0; i < trailCount - 1; i++)
                {
                    int currentIndex = (trailStartIndex + i) % maxTrailLength;
                    int nextIndex = (trailStartIndex + i + 1) % maxTrailLength;
                    Gizmos.DrawLine(trailPoints[currentIndex].Position, trailPoints[nextIndex].Position);
                }
            }
        }

        // Draw particle target positions
        if (showParticleTargets && debugParticleTargets != null)
        {
            Gizmos.color = Color.green;
            foreach (Vector3 target in debugParticleTargets)
            {
                Gizmos.DrawSphere(target, debugSphereSize * 0.7f);
            }
        }

        // Draw current transform position for reference
        Gizmos.color = Color.white;
        Gizmos.DrawSphere(transform.position, debugSphereSize * 2f);

        // Draw trail recording info
        if (enableTrailFollowing)
        {
            UnityEditor.Handles.color = Color.white;
            string info = $"Trail Count: {trailCount}/{maxTrailLength}\n";
            if (trailCount > 0)
            {
                int headIndex = trailCount == maxTrailLength ?
                    (trailStartIndex + trailCount - 1) % maxTrailLength :
                    (trailStartIndex + trailCount - 1);
                info += $"Trail Distance: {trailPoints[headIndex].DistanceFromStart:F1}";
            }
            UnityEditor.Handles.Label(transform.position + Vector3.up * 2f, info);
        }
    }
#endif

    #endregion


}