using UnityEngine;
using System.Collections.Generic;
using Random = UnityEngine.Random;

/// <summary>
/// Handles all movement-related behaviors for Soul Creatures using a weighted blending system.
/// Supports wandering, player mirroring, dolphin lunge, flocking, and orbiting behaviors.
/// </summary>
public class CreatureMovement : MonoBehaviour
{
    #region Core References and Boundary Settings

    // Core references
    private Transform playerTransform;
    private PlayerController playerController;
    private SoulCreatureLogic soulCreatureLogic;

    // Boundary values for movement constraints
    [Header("Movement Boundaries")]
    [Tooltip("Minimum Y position for movement")]
    [SerializeField] public float minY = 0f;

    [Tooltip("Maximum Y position for movement")]
    [SerializeField] public float maxY = 10f;

    // Maximum deltaTime to prevent large jumps during FPS drops
    [Tooltip("Maximum deltaTime to prevent large jumps during FPS drops")]
    [SerializeField] private float maxDeltaTime = 0.05f;

    [Header("Distance-Based Performance Settings")]
    [Tooltip("Distance from player where performance optimization starts")]
    [SerializeField] private float performanceOptimizationStartDistance = 10f;
    [Tooltip("Distance from player where maximum performance optimization is applied")]
    [SerializeField] private float performanceOptimizationMaxDistance = 30f;
    [Tooltip("Movement update interval for creatures close to player")]
    [SerializeField] private float minDistanceMovementUpdateInterval = 0f;
    [Tooltip("Movement update interval for creatures at maximum distance")]
    [SerializeField] private float maxDistanceMovementUpdateInterval = 0.1f;

    // Performance optimization variables
    private float movementUpdateTimer = 0f;
    private float currentMovementUpdateInterval = 0f; // 0 means update every frame
    private float accumulatedDeltaTime = 0f; // Accumulate deltaTime between updates

    #endregion

    #region Module Toggles

    [Header("Movement Module Toggles")]
    [Tooltip("Enable/disable weighted movement system")]
    public bool enableWeightedMovement = true;

    #endregion

    #region Weighted Movement System

    [Header("Weighted Movement Behaviors (0-1 range)")]
    [Tooltip("Weight for waypoint-based wandering behavior")]
    [Range(0f, 1f)] public float waypointWeight = 1f;

    [Tooltip("Weight for player movement mirroring behavior")]
    [Range(0f, 1f)] public float playerMirroringWeight = 0f;

    [Tooltip("Weight for direct attraction to player position")]
    [Range(0f, 1f)] public float playerAttractionWeight = 0f;

    [Tooltip("Weight for playful dolphin lunge behavior")]
    [Range(0f, 1f)] public float dolphinLungeWeight = 0f;

    [Tooltip("Weight for inter-creature flocking behavior")]
    [Range(0f, 1f)] public float flockingWeight = 0f;

    [Tooltip("Weight for player orbiting behavior")]
    [Range(0f, 1f)] public float orbitingWeight = 0f;

    // Internal weighted movement variables
    private Vector3 currentMovementDirection = Vector3.zero;

    #endregion

    #region Dolphin Lunge Settings

    [Header("Dolphin Lunge Settings")]
    [Tooltip("Duration of dolphin lunge in seconds")]
    public float lungeDuration = 3f;

    [Tooltip("Cooldown between lunges in seconds")]
    public float lungeCooldown = 2f;

    [Tooltip("Minimum distance from player required to trigger lunge")]
    public float lungeMinDistance = 1f;

    [Tooltip("Maximum offset from exact player position for lunge target")]
    public float lungeTargetOffset = 0f;

    [Tooltip("Y-axis offset for lunge target (positive = above player, negative = below player)")]
    public float lungeTargetYOffset = -3f;

    [Tooltip("Speed multiplier for dolphin lunge")]
    public float lungeSpeedMultiplier = 300f;

    // Internal dolphin lunge variables
    private float lastLungeTime = -Mathf.Infinity;
    private bool isLunging = false;

    #endregion

    #region Player Mirroring Settings

    [Header("Player Mirroring Settings")]
    [Tooltip("Minimum mirroring strength")]
    public float mirroringStrengthMin = 16f;

    [Tooltip("Maximum mirroring strength")]
    public float mirroringStrengthMax = 23f;

    [Tooltip("Maximum distance from player for mirroring to occur")]
    public float mirroringMaxDistance = 10f;

    [Tooltip("Minimum time between mirroring strength changes")]
    public float mirroringChangeIntervalMin = 6f;

    [Tooltip("Maximum time between mirroring strength changes")]
    public float mirroringChangeIntervalMax = 12f;

    // Internal mirroring variables
    public float currentMirroringStrength = 5f;
    private float nextMirroringChangeTime = 0f;

    #endregion

    #region Flocking Settings

    [Header("Flocking Settings")]
    [Tooltip("Range for detecting nearby soul creatures")]
    public float flockingRange = 15f;

    [Tooltip("Weight for cohesion (moving toward group center)")]
    [Range(0f, 1f)] public float cohesionWeight = 0.33f;

    [Tooltip("Weight for separation (avoiding crowding)")]
    [Range(0f, 1f)] public float separationWeight = 0.33f;

    [Tooltip("Weight for alignment (matching neighbors' velocity)")]
    [Range(0f, 1f)] public float alignmentWeight = 0.34f;

    [Tooltip("Minimum distance to maintain from other creatures")]
    public float separationDistance = 5f;

    #endregion

    #region Orbiting Settings

    [Header("Orbiting Settings")]
    [Tooltip("Radius of orbit around player")]
    public float orbitRadius = 12f;

    [Tooltip("Speed of orbital movement")]
    public float orbitSpeed = 1f;

    [Tooltip("Orbit type: 0=Horizontal, 1=Vertical, 2=Full 3D")]
    [Range(0, 2)] public int orbitType = 2;

    [Tooltip("Vertical range for 3D orbits (how far up/down from player)")]
    public float orbitVerticalRange = 12f;

    [Tooltip("Secondary orbit speed for complex 3D movement")]
    public float orbitSecondarySpeed = 0.7f;

    // Internal orbiting variables
    private float orbitAngle = 0f;

    #endregion

    #region Wandering Settings

    [Header("Wandering Movement Settings")]
    [Tooltip("Minimum movement speed for wandering")]
    [SerializeField] private float minMoveSpeed = 1.5f;

    [Tooltip("Maximum movement speed for wandering")]
    [SerializeField] private float maxMoveSpeed = 3f;

    [Tooltip("Current randomized movement speed")]
    [SerializeField] private float moveSpeed = 2f;

    [Tooltip("Speed of rotation towards target")]
    [SerializeField] private float rotationSpeed = 50f;

    [Tooltip("Minimum distance to generate a new target point")]
    [SerializeField] private float minDistanceToTarget = 5f;

    [Tooltip("Maximum distance to generate a new target point")]
    [SerializeField] private float maxDistanceToTarget = 20f;

    [Tooltip("Minimum amplitude of the wave motion")]
    [SerializeField] private float minWaveAmplitude = 0.2f;

    [Tooltip("Maximum amplitude of the wave motion")]
    [SerializeField] private float maxWaveAmplitude = 0.8f;

    [Tooltip("Minimum length of the wave motion")]
    [SerializeField] private float minWaveLength = 2f;

    [Tooltip("Maximum length of the wave motion")]
    [SerializeField] private float maxWaveLength = 5f;

    [Tooltip("Maximum attempts to generate a valid target point")]
    [SerializeField] private int maxPointGenerationAttempts = 10;

    [Tooltip("How close to get before considering target reached")]
    [SerializeField] private float targetReachedThreshold = 1.5f;

    [Tooltip("Maximum time (seconds) to pursue the same target before generating a new one")]
    [SerializeField] private float maxTargetPursuitTime = 15f;

    [Tooltip("Speed for player attraction movement")]
    [SerializeField] private float attractionSpeed = 5f;

    // Wandering internal variables
    private Vector3 currentTargetPoint;
    private Queue<Vector3> customTargets = new Queue<Vector3>();
    private bool hasCustomTargets = false;
    private float currentWaveAmplitude;
    private float currentWaveLength;
    private float waveOffset;
    private float currentTargetPursuitTime = 0f;

    #endregion

    #region Unity Lifecycle Methods

    private void Start()
    {
        // Get references from GameManager
        if (GameManager.Instance != null)
        {
            playerTransform = GameManager.Instance.player.transform;
            playerController = GameManager.Instance.player;
        }
        else
        {
            Debug.LogError("GameManager instance not found!");
            enabled = false;
            return;
        }

        // Get SoulCreatureLogic component
        soulCreatureLogic = GetComponent<SoulCreatureLogic>();
        if (soulCreatureLogic == null)
        {
            Debug.LogError("SoulCreatureLogic component not found!");
            enabled = false;
            return;
        }

        // Initialize movement settings
        InitializeMovementSettings();
    }

    private void Update()
    {
        if (playerTransform == null || playerController == null) return;

        // Calculate distance to player for performance optimization
        float distanceToPlayer = Vector3.Distance(transform.position, playerTransform.position);
        UpdateMovementPerformanceSettings(distanceToPlayer);

        // Always accumulate deltaTime
        accumulatedDeltaTime += Time.deltaTime;
        movementUpdateTimer += Time.deltaTime;

        // Check if we should update movement this frame
        if (currentMovementUpdateInterval > 0f && movementUpdateTimer < currentMovementUpdateInterval)
        {
            return; // Skip movement update this frame, but keep accumulating deltaTime
        }

        // Reset timers and use accumulated deltaTime for movement calculations
        movementUpdateTimer = 0f;
        float effectiveDeltaTime = accumulatedDeltaTime;
        accumulatedDeltaTime = 0f;

        // Update movement system with accumulated deltaTime
        if (enableWeightedMovement)
        {
            UpdateWeightedMovement(effectiveDeltaTime);
        }
    }

    #endregion

    #region Public Properties

    /// <summary>
    /// Current movement direction for flocking alignment behavior
    /// </summary>
    public Vector3 CurrentMovementDirection => currentMovementDirection;

    #endregion

    #region Initialization

    private void InitializeMovementSettings()
    {
        // Randomize movement settings
        moveSpeed = Random.Range(minMoveSpeed, maxMoveSpeed);

        // Initialize wandering
        waveOffset = Random.Range(0f, 2f * Mathf.PI);
        currentWaveAmplitude = Random.Range(minWaveAmplitude, maxWaveAmplitude);
        currentWaveLength = Random.Range(minWaveLength, maxWaveLength);
        orbitAngle = Random.Range(0f, 2f * Mathf.PI);
        GenerateNewTargetPoint();
    }

    #endregion

    #region Performance Optimization

    private void UpdateMovementPerformanceSettings(float distanceToPlayer)
    {
        // Calculate performance optimization factor based on distance
        float optimizationFactor = 0f;
        if (distanceToPlayer > performanceOptimizationStartDistance)
        {
            optimizationFactor = Mathf.InverseLerp(
                performanceOptimizationStartDistance,
                performanceOptimizationMaxDistance,
                distanceToPlayer
            );
        }

        // Update movement update interval based on distance
        currentMovementUpdateInterval = Mathf.Lerp(
            minDistanceMovementUpdateInterval, // Update interval when close
            maxDistanceMovementUpdateInterval, // Update interval when far
            optimizationFactor
        );
    }

    #endregion

    #region Weighted Movement System Implementation

    private void UpdateWeightedMovement(float deltaTime = 0f)
    {
        // Use provided deltaTime or fall back to Time.deltaTime for backward compatibility
        float actualDeltaTime = deltaTime > 0f ? deltaTime : Time.deltaTime;

        // For accumulated deltaTime (from performance optimization), don't clamp it
        // Only clamp when using regular Time.deltaTime to prevent large jumps during FPS drops
        float clampedDeltaTime = deltaTime > 0f ? actualDeltaTime : Mathf.Min(actualDeltaTime, maxDeltaTime);
        Vector3 finalMovement = Vector3.zero;

        // Calculate each behavior's contribution
        if (waypointWeight > 0f)
        {
            finalMovement += CalculateWaypointMovement() * waypointWeight;
        }

        if (playerMirroringWeight > 0f)
        {
            finalMovement += CalculatePlayerMirroringMovement() * playerMirroringWeight;
        }

        if (playerAttractionWeight > 0f)
        {
            finalMovement += CalculatePlayerAttractionMovement() * playerAttractionWeight;
        }

        // Handle dolphin lunge separately to avoid speed clamping
        Vector3 dolphinLungeMovement = Vector3.zero;
        if (dolphinLungeWeight > 0f)
        {
            dolphinLungeMovement = CalculateDolphinLungeMovement() * dolphinLungeWeight;
        }

        if (flockingWeight > 0f)
        {
            finalMovement += CalculateFlockingMovement() * flockingWeight;
        }

        if (orbitingWeight > 0f)
        {
            finalMovement += CalculateOrbitingMovement() * orbitingWeight;
        }

        // Store current movement direction
        Vector3 totalMovement = finalMovement + dolphinLungeMovement;
        currentMovementDirection = totalMovement.normalized;

        // Apply the final movement
        if (totalMovement.sqrMagnitude > 0.0001f)
        {
            // Apply dolphin lunge movement directly without deltaTime multiplication to preserve speed
            if (dolphinLungeMovement.sqrMagnitude > 0.0001f)
            {
                transform.position += dolphinLungeMovement * clampedDeltaTime;
            }

            // Apply other movements normally
            if (finalMovement.sqrMagnitude > 0.0001f)
            {
                transform.position += finalMovement * clampedDeltaTime;
            }

            // Ensure we don't go past boundaries
            Vector3 clampedPosition = transform.position;
            clampedPosition.y = Mathf.Clamp(clampedPosition.y, minY, maxY);
            transform.position = clampedPosition;

            // Apply smooth rotation towards movement direction
            if (totalMovement.sqrMagnitude > 0.01f)
            {
                Quaternion targetRotation = Quaternion.LookRotation(totalMovement.normalized);
                transform.rotation = Quaternion.Slerp(transform.rotation, targetRotation, rotationSpeed * clampedDeltaTime);
            }
        }
    }

    private Vector3 CalculateWaypointMovement()
    {
        // Use existing wandering logic but return movement vector instead of applying directly
        UpdateWanderingTarget();

        Vector3 directionToTarget = (currentTargetPoint - transform.position).normalized;
        Vector3 movement = directionToTarget * moveSpeed;

        // Add wave motion for natural swimming
        float waveTime = Time.time / currentWaveLength;
        Vector3 upDownMotion = Vector3.up * Mathf.Sin(waveTime * 2 * Mathf.PI + waveOffset) * currentWaveAmplitude;
        Vector3 leftRightMotion = Vector3.right * Mathf.Cos(waveTime * 2 * Mathf.PI + waveOffset + Mathf.PI / 4) * (currentWaveAmplitude * 0.7f);

        return movement + upDownMotion + leftRightMotion;
    }

    private Vector3 CalculatePlayerMirroringMovement()
    {
        // Check distance to player first
        float distanceToPlayer = Vector3.Distance(transform.position, playerTransform.position);
        if (distanceToPlayer > mirroringMaxDistance)
        {
            return Vector3.zero;
        }

        // Check if player is moving
        Vector3 playerMoveDir = playerController.lastMoveDirection3D;
        if (playerMoveDir.sqrMagnitude <= 0.0001f) return Vector3.zero;

        // Update mirroring strength based on timing
        UpdateMirroringStrength();

        return playerMoveDir.normalized * currentMirroringStrength;
    }

    private void UpdateMirroringStrength()
    {
        float currentTime = Time.time;

        // Check if it's time to change the mirroring strength
        if (currentTime >= nextMirroringChangeTime)
        {
            // Set new mirroring strength
            currentMirroringStrength = Random.Range(mirroringStrengthMin, mirroringStrengthMax);

            // Schedule next change
            float nextInterval = Random.Range(mirroringChangeIntervalMin, mirroringChangeIntervalMax);
            nextMirroringChangeTime = currentTime + nextInterval;
        }
    }

    private Vector3 CalculatePlayerAttractionMovement()
    {
        Vector3 attractionDirection = (playerTransform.position - transform.position).normalized;
        return attractionDirection * attractionSpeed;
    }

    private Vector3 CalculateDolphinLungeMovement()
    {
        float currentTime = Time.time;

        // Check distance to player
        float distanceToPlayer = Vector3.Distance(transform.position, playerTransform.position);

        // Only lunge if player is far enough away
        if (distanceToPlayer < lungeMinDistance)
        {
            return Vector3.zero;
        }

        // Check if we should start a new lunge
        if (!isLunging && currentTime - lastLungeTime >= lungeCooldown)
        {
            // Start new lunge
            isLunging = true;
            lastLungeTime = currentTime;
        }

        // Execute lunge if active
        if (isLunging)
        {
            if (currentTime - lastLungeTime < lungeDuration)
            {
                // Always target the player's current position with optional offset
                Vector3 offset = Random.insideUnitSphere * lungeTargetOffset;
                offset.y *= 0.5f; // Reduce random vertical offset
                Vector3 currentLungeTarget = playerTransform.position + offset;

                // Apply the Y offset setting
                currentLungeTarget.y += lungeTargetYOffset;

                // Ensure target is within boundaries
                currentLungeTarget.y = Mathf.Clamp(currentLungeTarget.y, minY, maxY);

                Vector3 lungeDirection = (currentLungeTarget - transform.position).normalized;
                return lungeDirection * moveSpeed * lungeSpeedMultiplier;
            }
            else
            {
                // End lunge
                isLunging = false;
            }
        }

        return Vector3.zero;
    }

    private Vector3 CalculateFlockingMovement()
    {
        Vector3 cohesion = Vector3.zero;
        Vector3 separation = Vector3.zero;
        Vector3 alignment = Vector3.zero;
        int neighborCount = 0;

        // Get all active soul creatures
        var allCreatures = SoulCreatureLogic.GetAllActiveInstances();

        foreach (var creature in allCreatures)
        {
            if (creature == null || creature.transform == transform) continue;

            float distance = Vector3.Distance(transform.position, creature.transform.position);
            if (distance <= flockingRange)
            {
                neighborCount++;

                // Cohesion: move toward group center
                cohesion += creature.transform.position;

                // Separation: avoid crowding
                if (distance < separationDistance && distance > 0.1f)
                {
                    Vector3 separationForce = (transform.position - creature.transform.position).normalized / distance;
                    separation += separationForce;
                }

                // Alignment: match neighbors' velocity
                // Get movement direction from creature's CreatureMovement component
                CreatureMovement creatureMovement = creature.GetComponent<CreatureMovement>();
                if (creatureMovement != null)
                {
                    alignment += creatureMovement.CurrentMovementDirection;
                }
            }
        }

        Vector3 flockingForce = Vector3.zero;

        if (neighborCount > 0)
        {
            // Cohesion
            cohesion = (cohesion / neighborCount - transform.position).normalized * moveSpeed * cohesionWeight;

            // Alignment
            alignment = (alignment / neighborCount).normalized * moveSpeed * alignmentWeight;

            // Combine forces
            flockingForce = cohesion + alignment;
        }

        // Add separation (calculated separately to avoid division issues)
        flockingForce += separation.normalized * moveSpeed * separationWeight;

        return flockingForce;
    }

    private Vector3 CalculateOrbitingMovement()
    {
        // Update orbit angle
        orbitAngle += orbitSpeed * Time.deltaTime;
        if (orbitAngle > 2 * Mathf.PI) orbitAngle -= 2 * Mathf.PI;

        // Calculate orbit position based on orbit type
        Vector3 orbitCenter = playerTransform.position;
        Vector3 orbitPosition;

        switch (orbitType)
        {
            case 0: // Horizontal orbit (XZ plane)
                {
                    float x = Mathf.Cos(orbitAngle) * orbitRadius;
                    float z = Mathf.Sin(orbitAngle) * orbitRadius;
                    orbitPosition = orbitCenter + new Vector3(x, 0, z);
                }
                break;

            case 1: // Vertical orbit (YZ plane or YX plane)
                {
                    float y = Mathf.Cos(orbitAngle) * orbitVerticalRange;
                    float z = Mathf.Sin(orbitAngle) * orbitRadius;
                    orbitPosition = orbitCenter + new Vector3(0, y, z);
                }
                break;

            case 2: // Full 3D orbit
            default:
                {
                    // Primary orbit in XZ plane
                    float x = Mathf.Cos(orbitAngle) * orbitRadius;
                    float z = Mathf.Sin(orbitAngle) * orbitRadius;

                    // Secondary orbit for Y movement with different frequency
                    float secondaryAngle = orbitAngle * orbitSecondarySpeed;
                    float y = Mathf.Sin(secondaryAngle) * orbitVerticalRange;

                    orbitPosition = orbitCenter + new Vector3(x, y, z);
                }
                break;
        }

        // Ensure orbit position is within boundaries
        orbitPosition.y = Mathf.Clamp(orbitPosition.y, minY, maxY);

        // Calculate movement toward orbit position
        Vector3 toOrbitPosition = (orbitPosition - transform.position).normalized;
        return toOrbitPosition * moveSpeed;
    }

    #endregion

    #region Wandering Implementation

    private void UpdateWanderingTarget()
    {
        // Update the target pursuit timer
        currentTargetPursuitTime += Time.deltaTime;

        // Check if we've been pursuing the same target for too long
        if (currentTargetPursuitTime > maxTargetPursuitTime)
        {
            if (hasCustomTargets)
            {
                customTargets.Clear();
                hasCustomTargets = false;
            }
            GenerateNewTargetPoint();
            currentTargetPursuitTime = 0f;
        }

        // Check if we should use a custom target point
        if (customTargets.Count > 0 && !hasCustomTargets)
        {
            currentTargetPoint = customTargets.Peek();
            hasCustomTargets = true;
            currentTargetPursuitTime = 0f;
        }

        // Check if we've reached the target
        float distanceToTarget = Vector3.Distance(transform.position, currentTargetPoint);
        if (distanceToTarget < targetReachedThreshold)
        {
            if (hasCustomTargets)
            {
                customTargets.Dequeue();
                if (customTargets.Count == 0)
                {
                    hasCustomTargets = false;
                    GenerateNewTargetPoint();
                }
                else
                {
                    currentTargetPoint = customTargets.Peek();
                }
            }
            else
            {
                GenerateNewTargetPoint();
                currentWaveAmplitude = Random.Range(minWaveAmplitude, maxWaveAmplitude);
                currentWaveLength = Random.Range(minWaveLength, maxWaveLength);
                waveOffset = Random.Range(0f, 2f * Mathf.PI);
            }
        }
    }
    private void GenerateNewTargetPoint()
    {
        Vector3 potentialTarget = Vector3.zero;
        bool validPointFound = false;
        int attempts = 0;

        // Try to find a valid point within our constraints
        while (!validPointFound && attempts < maxPointGenerationAttempts)
        {
            attempts++;

            // Choose a random distance within our range, but ensure it's not too close
            float minDistance = Mathf.Max(minDistanceToTarget, targetReachedThreshold);
            float targetDistance = Random.Range(minDistance, maxDistanceToTarget);

            // Limit the angle to be within 240 degrees of current forward direction
            // This prevents sharp turns by not allowing points directly behind
            float randomAngleY = Random.Range(-120f, 120f);
            float randomAngleX = Random.Range(-60f, 60f);

            // Create a potential direction
            Vector3 potentialDirection = Quaternion.Euler(randomAngleX, randomAngleY, 0) * transform.forward;

            // Calculate potential target
            potentialTarget = transform.position + potentialDirection * targetDistance;

            // Check if the target is fully within boundaries (including buffer)
            if (potentialTarget.y >= minY && potentialTarget.y <= maxY)
            {
                validPointFound = true;
            }
            // If we're near the edge, favor directions away from boundaries
            else if (attempts > maxPointGenerationAttempts / 2)
            {
                // Calculate center of valid space (independent of player position)
                Vector3 center = new Vector3(
                    transform.position.x,
                    (minY + maxY) / 2,
                    transform.position.z
                );

                // Weight direction toward vertical center if near boundaries
                Vector3 toCenter = new Vector3(
                    potentialDirection.x,
                    (center.y - transform.position.y) * 0.5f, // Only bias vertically
                    potentialDirection.z
                ).normalized;

                potentialTarget = transform.position + toCenter * targetDistance;

                // Ensure it's within bounds before accepting
                potentialTarget.y = Mathf.Clamp(potentialTarget.y, minY, maxY);

                validPointFound = true;
            }
        }

        // If we couldn't find a valid point after all attempts, clamp the last attempt to boundaries
        if (!validPointFound)
        {
            potentialTarget.y = Mathf.Clamp(potentialTarget.y, minY, maxY);
        }

        // Assign the new target
        currentTargetPoint = potentialTarget;
    }

    #endregion
}